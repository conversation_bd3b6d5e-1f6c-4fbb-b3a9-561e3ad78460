<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */
    'default' => env('DB_CONNECTION', 'mongodb'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    */
    'connections' => [
        /*
        |----------------------------------------------------------------------
        | MongoDB Connection
        |----------------------------------------------------------------------
        |
        | Configuration for MongoDB using the official MongoDB Laravel package.
        | Uses standard MongoDB connection URI format for better compatibility.
        |
        */
        'mongodb' => [
            'driver' => 'mongodb',
            'dsn' => env('DB_URI', 'mongodb://mongodb:27017'),
            'database' => env('DB_DATABASE', 'rapidsgspro'),
            'options' => [
                // Connection pool settings
                'maxPoolSize' => 10,
                'minPoolSize' => 1,
                'maxIdleTimeMS' => 30000,
                'serverSelectionTimeoutMS' => 30000,
                'connectTimeoutMS' => 10000,
                'socketTimeoutMS' => 30000,
                
                // Write concern for data safety
                'w' => 'majority',
                'wTimeoutMS' => 10000,
                
                // Read preference
                'readPreference' => 'primary',
                
                // Application name for monitoring
                'appName' => 'RapidSGSPro',
            ],
        ],

        /*
        |----------------------------------------------------------------------
        | SQLite Connection (for testing if needed)
        |----------------------------------------------------------------------
        */
        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */
    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */
    'redis' => [
        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
                            'prefix' => env('REDIS_PREFIX', str_replace(' ', '_', strtolower(env('APP_NAME', 'laravel'))).'_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],
    ],
];