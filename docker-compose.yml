services:

  mongodb-rapidsgspro:
    container_name: mongodb-rapidsgspro-dev
    image: mongo:7.0
    restart: unless-stopped
    ports:
      - "27017:27017"
    networks:
      - rapidsgspro-network
    volumes:
      - mongodb_data:/data/db

  app-rapidsgspro:
    container_name: app-rapidsgspro-dev
    build: .
    environment:
      - APP_URL=https://rapid5gs.com.local
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mongodb
      - DB_URI=mongodb://mongodb-rapidsgspro:27017
      - DB_HOST=mongodb-rapidsgspro
      - DB_PORT=27017
      - DB_DATABASE=rapidsgspro
      - MONGO_URI=mongodb://mongodb-rapidsgspro:27017/rapidsgspro
    volumes:
      - .:/var/www/html
    depends_on:
      - mongodb-rapidsgspro
    networks:
      - rapidsgspro-network

  vite-rapidsgspro:
    image: node:18-alpine
    container_name: vite-rapidsgspro-dev
    restart: unless-stopped
    working_dir: /var/www/html
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/var/www/html
    ports:
      - "5173:5173"
    networks:
      - rapidsgspro-network

  nginx-rapidsgspro:
    image: nginx:alpine
    container_name: nginx-rapidsgspro-dev
    restart: unless-stopped
    ports:
      - "8022:80"
    volumes:
      - ./nginx.dev.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app-rapidsgspro
    networks:
      - rapidsgspro-network

volumes:
  mongodb_data:

networks:
  rapidsgspro-network:
    driver: bridge