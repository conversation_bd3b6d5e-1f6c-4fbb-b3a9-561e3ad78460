<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class Enb extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'enbs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'cell_id', 'location', 'ip_address', 'frequency', 
        'bandwidth', 'power_level', 'status', 'created_at', 'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'power_level' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}