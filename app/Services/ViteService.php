<?php

namespace App\Services;

class ViteService
{
    protected $isDev;
    protected $manifest;
    protected $devServerUrl;

    public function __construct()
    {
        $this->isDev = env('APP_ENV') === 'local' || env('APP_ENV') === 'development';
        $this->devServerUrl = env('VITE_DEV_SERVER_URL', 'https://localhost:5173');
        
        if (!$this->isDev) {
            $this->loadManifest();
        }
    }

    protected function loadManifest()
    {
        $manifestPath = public_path('build/manifest.json');
        if (file_exists($manifestPath)) {
            $this->manifest = json_decode(file_get_contents($manifestPath), true);
        }
    }

    public function viteAssets($assets = ['resources/css/app.css', 'resources/js/app.js'])
    {
        if (!is_array($assets)) {
            $assets = [$assets];
        }

        $html = '';

        if ($this->isDev) {
            // Development mode - use current host to avoid CORS issues
            $baseUrl = $this->getCurrentHostUrl();
            
            $html .= '<script type="module" src="' . $baseUrl . '/@vite/client"></script>' . "\n";
            
            foreach ($assets as $asset) {
                if (str_ends_with($asset, '.css')) {
                    $html .= '<link rel="stylesheet" href="' . $baseUrl . '/' . $asset . '">' . "\n";
                } elseif (str_ends_with($asset, '.js')) {
                    $html .= '<script type="module" src="' . $baseUrl . '/' . $asset . '"></script>' . "\n";
                }
            }
        } else {
            // Production mode - use built assets
            if ($this->manifest) {
                foreach ($assets as $asset) {
                    if (isset($this->manifest[$asset])) {
                        $manifestEntry = $this->manifest[$asset];
                        
                        if (str_ends_with($asset, '.css') || !empty($manifestEntry['css'])) {
                            // CSS files
                            if (!empty($manifestEntry['css'])) {
                                foreach ($manifestEntry['css'] as $cssFile) {
                                    $html .= '<link rel="stylesheet" href="/build/' . $cssFile . '">' . "\n";
                                }
                            }
                            if (str_ends_with($asset, '.css')) {
                                $html .= '<link rel="stylesheet" href="/build/' . $manifestEntry['file'] . '">' . "\n";
                            }
                        }
                        
                        if (str_ends_with($asset, '.js')) {
                            // JS files
                            $html .= '<script type="module" src="/build/' . $manifestEntry['file'] . '"></script>' . "\n";
                        }
                    }
                }
            } else {
                // Fallback for missing manifest
                foreach ($assets as $asset) {
                    if (str_ends_with($asset, '.css')) {
                        $html .= '<link rel="stylesheet" href="' . $this->devServerUrl . '/' . $asset . '">' . "\n";
                    } elseif (str_ends_with($asset, '.js')) {
                        $html .= '<script type="module" src="' . $this->devServerUrl . '/' . $asset . '"></script>' . "\n";
                    }
                }
            }
        }

        return $html;
    }

    public function asset($path)
    {
        if ($this->isDev) {
            return $this->getCurrentHostUrl() . '/' . $path;
        } else {
            if ($this->manifest && isset($this->manifest[$path])) {
                return '/build/' . $this->manifest[$path]['file'];
            }
            return '/' . $path;
        }
    }

    protected function getCurrentHostUrl()
    {
        // Try to get current host from request
        if (isset($_SERVER['HTTP_HOST'])) {
            $protocol = 'https';
            return $protocol . '://' . $_SERVER['HTTP_HOST'];
        }
        
        // Fallback to environment variable or default
        return $this->devServerUrl;
    }
} 