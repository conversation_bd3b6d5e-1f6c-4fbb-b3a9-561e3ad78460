<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Services\ViteService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Register ViteService
        $this->app->singleton(ViteService::class, function ($app) {
            return new ViteService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Create Blade directive for Vite assets
        Blade::directive('viteAssets', function ($expression) {
            $expression = $expression ?: "['resources/css/app.css', 'resources/js/app.js']";
            return "<?php echo app(\\App\\Services\\ViteService::class)->viteAssets({$expression}); ?>";
        });
    }
} 