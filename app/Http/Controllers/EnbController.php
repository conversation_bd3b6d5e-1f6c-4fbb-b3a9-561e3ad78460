<?php

namespace App\Http\Controllers;

use App\Models\Enb;
use Illuminate\Http\Request;

class Enb<PERSON>ontroll<PERSON> extends Controller
{
    /**
     * Display the eNBs management view.
     */
    public function index()
    {
        return view('enbs');
    }

    /**
     * Get eNBs data for API requests.
     */
    public function apiIndex(Request $request)
    {
        $query = Enb::query();

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('cell_id', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $enbs = $query->latest()->get();

        return $this->respondWithSuccess($enbs);
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|string|max:255',
            'cell_id' => 'required|string|unique:enbs',
            'location' => 'required|string',
            'ip_address' => 'required|ip',
            'frequency' => 'nullable|string',
            'bandwidth' => 'nullable|string',
            'power_level' => 'nullable|numeric',
            'status' => 'in:online,offline,maintenance'
        ]);

        $enb = Enb::create([
            'name' => $request->name,
            'cell_id' => $request->cell_id,
            'location' => $request->location,
            'ip_address' => $request->ip_address,
            'frequency' => $request->frequency,
            'bandwidth' => $request->bandwidth,
            'power_level' => $request->power_level,
            'status' => $request->status ?? 'offline',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return $this->respondWithSuccess($enb, 'eNB created successfully', 201);
    }

    public function show($id)
    {
        $enb = Enb::find($id);
        
        if (!$enb) {
            return $this->respondWithError('eNB not found', 404);
        }

        return $this->respondWithSuccess($enb);
    }

    public function update(Request $request, $id)
    {
        $enb = Enb::find($id);
        
        if (!$enb) {
            return $this->respondWithError('eNB not found', 404);
        }

        $this->validate($request, [
            'name' => 'string|max:255',
            'cell_id' => 'string|unique:enbs,cell_id,' . $id,
            'location' => 'string',
            'ip_address' => 'ip',
            'frequency' => 'nullable|string',
            'bandwidth' => 'nullable|string',
            'power_level' => 'nullable|numeric',
            'status' => 'in:online,offline,maintenance'
        ]);

        $updateData = $request->only(['name', 'cell_id', 'location', 'ip_address', 'frequency', 'bandwidth', 'power_level', 'status']);
        $updateData['updated_at'] = now();

        $enb->update($updateData);

        return $this->respondWithSuccess($enb, 'eNB updated successfully');
    }

    public function destroy($id)
    {
        $enb = Enb::find($id);
        
        if (!$enb) {
            return $this->respondWithError('eNB not found', 404);
        }

        $enb->delete();

        return $this->respondWithSuccess(null, 'eNB deleted successfully');
    }

    public function stats()
    {
        $total = Enb::count();
        $online = Enb::where('status', 'online')->count();
        $offline = Enb::where('status', 'offline')->count();
        $maintenance = Enb::where('status', 'maintenance')->count();

        return $this->respondWithSuccess([
            'total' => $total,
            'online' => $online,
            'offline' => $offline + $maintenance
        ]);
    }
} 