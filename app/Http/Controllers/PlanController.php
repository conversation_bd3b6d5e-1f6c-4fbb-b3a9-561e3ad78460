<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\User;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    /**
     * Display the plans management view.
     */
    public function index()
    {
        return view('plans');
    }

    /**
     * Get plans data for API requests.
     */
    public function apiIndex(Request $request)
    {
        $query = Plan::query();

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $plans = $query->latest()->get();
        
        // Add user count for each plan
        $plans->each(function($plan) {
            $plan->user_count = User::where('plan_id', $plan->_id)->count();
        });

        return $this->respondWithSuccess($plans);
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'data_limit' => 'nullable|string',
            'speed' => 'nullable|string',
            'support_level' => 'in:basic,standard,premium',
            'status' => 'in:active,inactive'
        ]);

        $plan = Plan::create([
            'name' => $request->name,
            'description' => $request->description,
            'price' => $request->price,
            'data_limit' => $request->data_limit,
            'speed' => $request->speed,
            'support_level' => $request->support_level ?? 'basic',
            'status' => $request->status ?? 'active',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return $this->respondWithSuccess($plan, 'Plan created successfully', 201);
    }

    public function show($id)
    {
        $plan = Plan::find($id);
        
        if (!$plan) {
            return $this->respondWithError('Plan not found', 404);
        }

        // Add user count
        $plan->user_count = User::where('plan_id', $plan->_id)->count();

        return $this->respondWithSuccess($plan);
    }

    public function update(Request $request, $id)
    {
        $plan = Plan::find($id);
        
        if (!$plan) {
            return $this->respondWithError('Plan not found', 404);
        }

        $this->validate($request, [
            'name' => 'string|max:255',
            'description' => 'nullable|string',
            'price' => 'numeric|min:0',
            'data_limit' => 'nullable|string',
            'speed' => 'nullable|string',
            'support_level' => 'in:basic,standard,premium',
            'status' => 'in:active,inactive'
        ]);

        $updateData = $request->only(['name', 'description', 'price', 'data_limit', 'speed', 'support_level', 'status']);
        $updateData['updated_at'] = now();

        $plan->update($updateData);

        return $this->respondWithSuccess($plan, 'Plan updated successfully');
    }

    public function destroy($id)
    {
        $plan = Plan::find($id);
        
        if (!$plan) {
            return $this->respondWithError('Plan not found', 404);
        }

        // Check if plan is being used by any users
        $usersCount = User::where('plan_id', $plan->_id)->count();
        if ($usersCount > 0) {
            return $this->respondWithError('Cannot delete plan that is assigned to users', 400);
        }

        $plan->delete();

        return $this->respondWithSuccess(null, 'Plan deleted successfully');
    }

    public function stats()
    {
        $total = Plan::count();
        $active = Plan::where('status', 'active')->count();
        $inactive = Plan::where('status', 'inactive')->count();

        return $this->respondWithSuccess([
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive
        ]);
    }
}