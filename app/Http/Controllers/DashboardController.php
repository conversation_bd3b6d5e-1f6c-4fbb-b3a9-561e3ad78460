<?php

namespace App\Http\Controllers;

class DashboardController extends Controller
{
    /**
     * Display the dashboard view.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('dashboard');
    }
    
    /**
     * API endpoint for dashboard statistics
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        $stats = [
            'download' => 420,
            'upload' => 120,
            'uptime' => 640,
            'users' => [
                'total' => 1247,
                'active' => 834,
                'inactive' => 413
            ],
            'plans' => [
                'total' => 15,
                'active' => 12,
                'inactive' => 3
            ],
            'enbs' => [
                'total' => 89,
                'online' => 76,
                'offline' => 13
            ],
            'latency' => [
              'google' => 15,
              'Cloudflare' => 10,
              'Netflix' => 20
            ]
        ];
        
        return $this->respondWithSuccess($stats, 'Dashboard statistics retrieved successfully');
    }
} 