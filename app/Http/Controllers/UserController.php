<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    /**
     * Display the users management view.
     */
    public function index(): \Illuminate\View\View
    {
        return view('users');
    }

    /**
     * Get users data for API requests.
     */
    public function apiIndex(Request $request): \Illuminate\Http\JsonResponse
    {
        // Temporary mock data until MongoDB connection is fixed
        $users = [
            [
                'imsi' => '0010100000000001',
                'name' => '<PERSON>',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['EDGE', 'UE-MGMT', '50M'],
                'latency' => '8ms'
            ],
            [
                'imsi' => '0010100000000002',
                'name' => '<PERSON>',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['EDGE', '100M'],
                'latency' => '5ms'
            ],
            [
                'imsi' => '0010100000000003',
                'name' => 'Robert Johnson',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['UE-MGMT', '200M'],
                'latency' => '12ms'
            ],
            [
                'imsi' => '0010100000000004',
                'name' => 'Emily Davis',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['EDGE', 'UE-MGMT', '500M'],
                'latency' => '7ms'
            ],
            [
                'imsi' => '0010100000000005',
                'name' => 'Michael Wilson',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['1G'],
                'latency' => '3ms'
            ],
            [
                'imsi' => '0010100000000006',
                'name' => 'Sarah Brown',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['EDGE', '50M'],
                'latency' => '9ms'
            ],
            [
                'imsi' => '0010100000000007',
                'name' => 'David Miller',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['UE-MGMT', '100M'],
                'latency' => '6ms'
            ],
            [
                'imsi' => '0010100000000008',
                'name' => 'Jessica Garcia',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['EDGE', 'UE-MGMT', '200M'],
                'latency' => '11ms'
            ],
            [
                'imsi' => '0010100000000009',
                'name' => 'Thomas Martinez',
                'ip_address' => '*********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['500M'],
                'latency' => '4ms'
            ],
            [
                'imsi' => '0010100000000010',
                'name' => 'Lisa Anderson',
                'ip_address' => '**********',
                'ki' => '*****************',
                'opc' => '***************',
                'plans' => ['EDGE', '1G'],
                'latency' => '2ms'
            ]
        ];

        // Apply filters if provided
        if ($request->has('search')) {
            $search = strtolower($request->search);
            $users = array_filter($users, function($user) use ($search) {
                return stripos($user['name'], $search) !== false || 
                       stripos($user['email'], $search) !== false;
            });
        }

        if ($request->has('plan')) {
            $plan = $request->plan;
            $users = array_filter($users, function($user) use ($plan) {
                return $user['plan_id'] === $plan;
            });
        }

        if ($request->has('status')) {
            $status = $request->status;
            $users = array_filter($users, function($user) use ($status) {
                return $user['status'] === $status;
            });
        }

        return $this->respondWithSuccess(array_values($users));
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'plan_id' => 'nullable|string',
            'status' => 'in:active,inactive'
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'plan_id' => $request->plan_id,
            'status' => $request->status ?? 'active',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return $this->respondWithSuccess($user, 'User created successfully', 201);
    }

    public function show($id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->respondWithError('User not found', 404);
        }

        return $this->respondWithSuccess($user);
    }

    public function update(Request $request, $id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->respondWithError('User not found', 404);
        }

        $this->validate($request, [
            'name' => 'string|max:255',
            'email' => 'email|unique:users,email,' . $id,
            'plan_id' => 'nullable|string',
            'status' => 'in:active,inactive'
        ]);

        $updateData = $request->only(['name', 'email', 'plan_id', 'status']);
        $updateData['updated_at'] = now();

        $user->update($updateData);

        return $this->respondWithSuccess($user, 'User updated successfully');
    }

    public function destroy($id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->respondWithError('User not found', 404);
        }

        $user->delete();

        return $this->respondWithSuccess(null, 'User deleted successfully');
    }

    public function stats()
    {
        // Mock stats data
        return $this->respondWithSuccess([
            'total' => 3,
            'active' => 2,
            'inactive' => 1
        ]);
    }
}