#!/bin/bash

# RapidSGS Pro - Deployment Script

set -e

MODE=${1:-development}

echo "🚀 Deploying RapidSGS Pro in $MODE mode..."

if [ "$MODE" = "production" ]; then
    echo "🏭 Production Deployment (without Docker)"
    
    # Install PHP dependencies
    echo "📦 Installing PHP dependencies..."
    composer install --no-dev --optimize-autoloader
    
    # Install Node.js dependencies and build assets
    echo "🔨 Building frontend assets..."
    npm ci
    npm run build
    
    # Set proper permissions
    echo "🔐 Setting permissions..."
    chmod -R 755 storage
    chmod -R 755 bootstrap/cache
    
    # Clear cache
    echo "🧹 Clearing caches..."
    rm -rf storage/framework/views/*
    
    echo "✅ Production deployment completed!"
    echo "📁 Project ready in: $(pwd)"
    echo "🌐 Configure your web server to point to: $(pwd)/public"
    echo "🔧 Make sure MongoDB is running and configured"
    
elif [ "$MODE" = "development" ]; then
    echo "🛠️  Development Deployment (with Docker)"
    
    # Stop existing containers
    echo "🛑 Stopping existing containers..."
    docker compose down 2>/dev/null || true
    
    # Start development services
    echo "🏗️  Starting development services..."
    docker compose up -d --build
    
    echo "✅ Development deployment completed!"
    echo "🌐 Application: http://rapid5gs.com.local:8022"
    echo "🔥 Vite HMR: http://localhost:5173"
    
else
    echo "❌ Invalid mode. Use 'development' or 'production'"
    echo "Usage: ./deploy.sh [development|production]"
    exit 1
fi

echo "📊 MongoDB: localhost:27017"
echo ""
echo "🎉 Deployment complete!" 