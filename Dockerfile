# Dockerfile
FROM ubuntu:24.04

# Установка базовых пакетов и добавление PHP репозитория
RUN apt-get update && apt-get install -y \
    software-properties-common \
    curl \
    wget \
    gnupg2 \
    && add-apt-repository ppa:ondrej/php \
    && apt-get update

# Установка PHP 8.2 и необходимых расширений
RUN apt-get install -y \
    php8.2-fpm \
    php8.2-cli \
    php8.2-common \
    php8.2-mysql \
    php8.2-zip \
    php8.2-gd \
    php8.2-mbstring \
    php8.2-curl \
    php8.2-xml \
    php8.2-bcmath \
    php8.2-intl \
    php8.2-opcache \
    php8.2-calendar \
    php8.2-soap \
    php8.2-dev \
    libssl-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Установка Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Установка MongoDB PHP extension
RUN pecl install mongodb \
    && echo "extension=mongodb.so" > /etc/php/8.2/fpm/conf.d/30-mongodb.ini \
    && echo "extension=mongodb.so" > /etc/php/8.2/cli/conf.d/30-mongodb.ini

# Установка XDebug
RUN pecl install xdebug \
    && echo "zend_extension=xdebug.so" > /etc/php/8.2/fpm/conf.d/20-xdebug.ini \
    && echo "zend_extension=xdebug.so" > /etc/php/8.2/cli/conf.d/20-xdebug.ini

# Копирование конфигурации XDebug
COPY ./xdebug.ini /etc/php/8.2/fpm/conf.d/99-xdebug.ini
COPY ./xdebug.ini /etc/php/8.2/cli/conf.d/99-xdebug.ini

# Настройка PHP-FPM
RUN sed -i 's/listen = \/run\/php\/php8.2-fpm.sock/listen = 9000/' /etc/php/8.2/fpm/pool.d/www.conf

# Создание рабочей директории
WORKDIR /var/www/html

# Запуск PHP-FPM
EXPOSE 9000
CMD ["php-fpm8.2", "-F"]