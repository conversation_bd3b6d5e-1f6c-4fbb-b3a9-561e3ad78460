<?php

use <PERSON><PERSON>\Lumen\Routing\Router;

/** @var Router $router */

// Dashboard route
$router->get('/', 'DashboardController@index');
$router->get('/dashboard', 'DashboardController@index');

// Users routes
$router->get('/users', 'UserController@index');

// Plans routes  
$router->get('/plans', 'PlanController@index');

// eNBs routes
$router->get('/enbs', 'EnbController@index');

// API routes for AJAX requests
$router->group(['prefix' => 'api'], function () use ($router) {
    // Dashboard API
    $router->get('/dashboard/stats', 'DashboardController@getStats');
    
    // Users API
    $router->get('/users', 'UserController@apiIndex');
    $router->post('/users', 'UserController@store');
    $router->get('/users/{id}', 'UserController@show');
    $router->put('/users/{id}', 'UserController@update');
    $router->delete('/users/{id}', 'UserController@destroy');
    $router->get('/users/stats', 'UserController@stats');
    
    // Plans API
    $router->get('/plans', 'PlanController@apiIndex');
    $router->post('/plans', 'PlanController@store');
    $router->get('/plans/{id}', 'PlanController@show');
    $router->put('/plans/{id}', 'PlanController@update');
    $router->delete('/plans/{id}', 'PlanController@destroy');
    $router->get('/plans/stats', 'PlanController@stats');
    
    // eNBs API
    $router->get('/enbs', 'EnbController@apiIndex');
    $router->post('/enbs', 'EnbController@store');
    $router->get('/enbs/{id}', 'EnbController@show');
    $router->put('/enbs/{id}', 'EnbController@update');
    $router->delete('/enbs/{id}', 'EnbController@destroy');
    $router->get('/enbs/stats', 'EnbController@stats');
}); 