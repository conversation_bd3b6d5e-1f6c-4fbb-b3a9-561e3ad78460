<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'id' => 'formModal',
    'title' => 'Форма',
    'submitText' => 'Сохранить',
    'cancelText' => 'Отмена',
    'submitClass' => 'bg-blue-600 hover:bg-blue-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'lg',
    'method' => 'POST',
    'action' => '#',
    'formId' => null
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'id' => 'formModal',
    'title' => 'Форма',
    'submitText' => 'Сохранить',
    'cancelText' => 'Отмена',
    'submitClass' => 'bg-blue-600 hover:bg-blue-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'lg',
    'method' => 'POST',
    'action' => '#',
    'formId' => null
]); ?>
<?php foreach (array_filter(([
    'id' => 'formModal',
    'title' => 'Форма',
    'submitText' => 'Сохранить',
    'cancelText' => 'Отмена',
    'submitClass' => 'bg-blue-600 hover:bg-blue-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'lg',
    'method' => 'POST',
    'action' => '#',
    'formId' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
$formId = $formId ?: $id . 'Form';
?>

<?php if (isset($component)) { $__componentOriginal9f64f32e90b9102968f2bc548315018c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9f64f32e90b9102968f2bc548315018c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modal','data' => ['id' => $id,'title' => $title,'size' => $size,'animation' => 'slide']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($id),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($title),'size' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($size),'animation' => 'slide']); ?>
     <?php $__env->slot('body', null, []); ?> 
        <form id="<?php echo e($formId); ?>" @submit.prevent="submitForm($event)" class="space-y-4" data-method="<?php echo e($method); ?>" data-action="<?php echo e($action); ?>">
            <?php echo e($slot); ?>

        </form>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('footer', null, []); ?> 
        <div class="flex justify-end space-x-3">
            <button type="button" 
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 <?php echo e($cancelClass); ?>"
                    @click="$dispatch('close-modal-<?php echo e($id); ?>')">
                <?php echo e($cancelText); ?>

            </button>
            <button type="submit" 
                    form="<?php echo e($formId); ?>"
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 <?php echo e($submitClass); ?>">
                <?php echo e($submitText); ?>

            </button>
        </div>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9f64f32e90b9102968f2bc548315018c)): ?>
<?php $attributes = $__attributesOriginal9f64f32e90b9102968f2bc548315018c; ?>
<?php unset($__attributesOriginal9f64f32e90b9102968f2bc548315018c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9f64f32e90b9102968f2bc548315018c)): ?>
<?php $component = $__componentOriginal9f64f32e90b9102968f2bc548315018c; ?>
<?php unset($__componentOriginal9f64f32e90b9102968f2bc548315018c); ?>
<?php endif; ?> <?php /**PATH /var/www/html/resources/views/components/form-modal.blade.php ENDPATH**/ ?>