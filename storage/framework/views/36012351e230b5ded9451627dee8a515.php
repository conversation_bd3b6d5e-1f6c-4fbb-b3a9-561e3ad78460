<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'id' => 'confirmModal',
    'title' => 'Подтверждение',
    'message' => 'Вы уверены, что хотите выполнить это действие?',
    'confirmText' => 'Подтвердить',
    'cancelText' => 'Отмена',
    'confirmClass' => 'bg-red-600 hover:bg-red-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'md'
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'id' => 'confirmModal',
    'title' => 'Подтверждение',
    'message' => 'Вы уверены, что хотите выполнить это действие?',
    'confirmText' => 'Подтвердить',
    'cancelText' => 'Отмена',
    'confirmClass' => 'bg-red-600 hover:bg-red-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'md'
]); ?>
<?php foreach (array_filter(([
    'id' => 'confirmModal',
    'title' => 'Подтверждение',
    'message' => 'Вы уверены, что хотите выполнить это действие?',
    'confirmText' => 'Подтвердить',
    'cancelText' => 'Отмена',
    'confirmClass' => 'bg-red-600 hover:bg-red-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'md'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php if (isset($component)) { $__componentOriginal9f64f32e90b9102968f2bc548315018c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9f64f32e90b9102968f2bc548315018c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modal','data' => ['id' => $id,'title' => $title,'size' => $size,'animation' => 'zoom']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($id),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($title),'size' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($size),'animation' => 'zoom']); ?>
     <?php $__env->slot('body', null, []); ?> 
        <div class="text-center">
            <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <p class="text-gray-700 mb-6"><?php echo e($message); ?></p>
        </div>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('footer', null, []); ?> 
        <div class="flex justify-end space-x-3">
            <button type="button" 
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 <?php echo e($cancelClass); ?>"
                    @click="$dispatch('close-modal-<?php echo e($id); ?>')">
                <?php echo e($cancelText); ?>

            </button>
            <button type="button" 
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 <?php echo e($confirmClass); ?>"
                    @click="$dispatch('modal-confirmed-<?php echo e($id); ?>'); $dispatch('close-modal-<?php echo e($id); ?>')">
                <?php echo e($confirmText); ?>

            </button>
        </div>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9f64f32e90b9102968f2bc548315018c)): ?>
<?php $attributes = $__attributesOriginal9f64f32e90b9102968f2bc548315018c; ?>
<?php unset($__attributesOriginal9f64f32e90b9102968f2bc548315018c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9f64f32e90b9102968f2bc548315018c)): ?>
<?php $component = $__componentOriginal9f64f32e90b9102968f2bc548315018c; ?>
<?php unset($__componentOriginal9f64f32e90b9102968f2bc548315018c); ?>
<?php endif; ?> <?php /**PATH /var/www/html/resources/views/components/confirm-modal.blade.php ENDPATH**/ ?>