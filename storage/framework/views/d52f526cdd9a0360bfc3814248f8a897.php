<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'id' => 'modal',
    'size' => 'md', // sm, md, lg, xl, full
    'title' => '',
    'closable' => true,
    'backdrop' => true,
    'centered' => true,
    'animation' => 'fade', // fade, slide, zoom
    'maxWidth' => null
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'id' => 'modal',
    'size' => 'md', // sm, md, lg, xl, full
    'title' => '',
    'closable' => true,
    'backdrop' => true,
    'centered' => true,
    'animation' => 'fade', // fade, slide, zoom
    'maxWidth' => null
]); ?>
<?php foreach (array_filter(([
    'id' => 'modal',
    'size' => 'md', // sm, md, lg, xl, full
    'title' => '',
    'closable' => true,
    'backdrop' => true,
    'centered' => true,
    'animation' => 'fade', // fade, slide, zoom
    'maxWidth' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
$sizeClasses = [
    'sm' => 'max-w-sm',
    'md' => 'max-w-md', 
    'lg' => 'max-w-lg',
    'xl' => 'max-w-xl',
    '2xl' => 'max-w-2xl',
    '3xl' => 'max-w-3xl',
    '4xl' => 'max-w-4xl',
    'full' => 'max-w-full'
];

$animationClasses = [
    'fade' => 'modal-fade',
    'slide' => 'modal-slide', 
    'zoom' => 'modal-zoom'
];

$modalSize = $maxWidth ? "max-w-[$maxWidth]" : ($sizeClasses[$size] ?? $sizeClasses['md']);
$modalAnimation = $animationClasses[$animation] ?? $animationClasses['fade'];
?>

<!-- Modal backdrop -->
<div x-data="{ open: false }" 
     x-show="open" 
     x-on:open-modal-<?php echo e($id); ?>.window="open = true"
     x-on:close-modal-<?php echo e($id); ?>.window="open = false"
     x-on:keydown.escape.window="open = false"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto"
     style="display: none;">
     
    <!-- Background overlay -->
    <?php if($backdrop): ?>
    <div class="fixed inset-0 bg-gray-900 bg-opacity-50 transition-opacity"
         <?php if($closable): ?> @click="open = false" <?php endif; ?>>
    </div>
    <?php endif; ?>

    <!-- Modal container -->
    <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0 <?php echo e($centered ? 'items-center' : 'items-start pt-16'); ?>">
        
        <!-- Modal content -->
        <div x-show="open"
             x-transition:enter="ease-out duration-300 <?php echo e($modalAnimation); ?>-enter"
             x-transition:enter-start="<?php echo e($modalAnimation); ?>-enter-start"
             x-transition:enter-end="<?php echo e($modalAnimation); ?>-enter-end"
             x-transition:leave="ease-in duration-200 <?php echo e($modalAnimation); ?>-leave"
             x-transition:leave-start="<?php echo e($modalAnimation); ?>-leave-start"
             x-transition:leave-end="<?php echo e($modalAnimation); ?>-leave-end"
             @click.stop
             class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all w-full <?php echo e($modalSize); ?>">
            
            <!-- Modal header -->
            <?php if($title || $closable || isset($header)): ?>
            <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <?php if(isset($header)): ?>
                    <?php echo e($header); ?>

                <?php else: ?>
                    <h3 class="text-lg font-medium text-gray-900">
                        <?php echo e($title); ?>

                    </h3>
                <?php endif; ?>
                
                <?php if($closable): ?>
                <button type="button" 
                        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md p-2"
                        @click="open = false">
                    <span class="sr-only">Close</span>
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Modal body -->
            <div class="px-6 py-4">
                <?php if(isset($body)): ?>
                    <?php echo e($body); ?>

                <?php else: ?>
                    <?php echo e($slot); ?>

                <?php endif; ?>
            </div>

            <!-- Modal footer -->
            <?php if(isset($footer)): ?>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <?php echo e($footer); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Modal animations */
.modal-fade-enter { transition: opacity 300ms ease-out, transform 300ms ease-out; }
.modal-fade-enter-start { opacity: 0; transform: scale(0.95); }
.modal-fade-enter-end { opacity: 1; transform: scale(1); }
.modal-fade-leave { transition: opacity 200ms ease-in, transform 200ms ease-in; }
.modal-fade-leave-start { opacity: 1; transform: scale(1); }
.modal-fade-leave-end { opacity: 0; transform: scale(0.95); }

.modal-slide-enter { transition: opacity 300ms ease-out, transform 300ms ease-out; }
.modal-slide-enter-start { opacity: 0; transform: translateY(-50px); }
.modal-slide-enter-end { opacity: 1; transform: translateY(0); }
.modal-slide-leave { transition: opacity 200ms ease-in, transform 200ms ease-in; }
.modal-slide-leave-start { opacity: 1; transform: translateY(0); }
.modal-slide-leave-end { opacity: 0; transform: translateY(-50px); }

.modal-zoom-enter { transition: opacity 300ms ease-out, transform 300ms ease-out; }
.modal-zoom-enter-start { opacity: 0; transform: scale(0.8); }
.modal-zoom-enter-end { opacity: 1; transform: scale(1); }
.modal-zoom-leave { transition: opacity 200ms ease-in, transform 200ms ease-in; }
.modal-zoom-leave-start { opacity: 1; transform: scale(1); }
.modal-zoom-leave-end { opacity: 0; transform: scale(0.8); }
</style> <?php /**PATH /var/www/html/resources/views/components/modal.blade.php ENDPATH**/ ?>