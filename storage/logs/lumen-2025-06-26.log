[2025-06-26 04:15:05] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:16:52] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:18:03] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:18:13] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:18:35] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:18:49] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:20:23] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:21:02] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:22:28] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:22:58] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:25:00] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:25:20] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:27:23] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:27:52] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:29:07] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:33:52] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:37:49] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:39:17] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:40:24] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:40:31] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:42:47] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:43:22] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:43:51] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:44:21] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:44:33] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:45:09] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 04:45:40] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 05:57:04] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 05:57:04] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 05:57:37] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 05:57:44] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:03:59] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:04:02] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:04:38] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:04:42] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:05:48] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:05:54] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:26] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:30] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:30] local.ERROR: No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve 'mongodb']. Topology type: Single {"exception":"[object] (MongoDB\\Driver\\Exception\\ConnectionTimeoutException(code: 13053): No suitable servers found (`serverSelectionTryOnce` set): [Failed to resolve 'mongodb']. Topology type: Single at /var/www/html/vendor/mongodb/mongodb/src/functions.php:605)
[stacktrace]
#0 /var/www/html/vendor/mongodb/mongodb/src/functions.php(605): MongoDB\\Driver\\Manager->selectServer()
#1 /var/www/html/vendor/mongodb/mongodb/src/Collection.php(646): MongoDB\\select_server()
#2 /var/www/html/vendor/mongodb/laravel-mongodb/src/Query/Builder.php(525): MongoDB\\Collection->find()
#3 /var/www/html/vendor/mongodb/laravel-mongodb/src/Query/Builder.php(262): MongoDB\\Laravel\\Query\\Builder->getFresh()
#4 /var/www/html/vendor/illuminate/database/Eloquent/Builder.php(739): MongoDB\\Laravel\\Query\\Builder->get()
#5 /var/www/html/vendor/illuminate/database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels()
#6 /var/www/html/app/Http/Controllers/PlanController.php(38): Illuminate\\Database\\Eloquent\\Builder->get()
#7 /var/www/html/vendor/illuminate/container/BoundMethod.php(36): App\\Http\\Controllers\\PlanController->apiIndex()
#8 /var/www/html/vendor/illuminate/container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/vendor/illuminate/container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/vendor/illuminate/container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/vendor/illuminate/container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(391): Illuminate\\Container\\Container->call()
#13 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(356): Laravel\\Lumen\\Application->callControllerCallable()
#14 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController()
#15 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction()
#16 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute()
#17 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute()
#18 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#19 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#20 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#21 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#22 {main}
"} 
[2025-06-26 06:06:34] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:38] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:42] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:45] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:49] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:06:57] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:07:00] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:09:48] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:11:07] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:12:11] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:12:38] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:12:53] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:13:49] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:16:07] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:17:35] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:18:02] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:19:04] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:20:25] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:20:28] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:20:31] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:20:34] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:20:40] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:23:20] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:23:23] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:23:27] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:23:34] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-26 06:25:33] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
