[2025-06-25 13:32:32] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:32:33] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:45:48] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:46:52] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:47:58] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:49:10] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:51:46] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:58:40] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:59:19] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 13:59:43] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:00:53] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:03:40] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:10:41] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:18:44] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:19:17] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:20:12] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:20:37] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
[2025-06-25 14:24:31] local.ERROR: Static route "/api/users/stats" is shadowed by previously defined variable route "/api/users/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/api/users/stats\" is shadowed by previously defined variable route \"/api/users/([^/]+)\" for method \"GET\" at /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php:95)
[stacktrace]
#0 /var/www/html/vendor/nikic/fast-route/src/DataGenerator/RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute()
#1 /var/www/html/vendor/nikic/fast-route/src/RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute()
#2 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute()
#3 /var/www/html/vendor/nikic/fast-route/src/functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#4 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(207): FastRoute\\simpleDispatcher()
#5 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}()
#7 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline()
#8 /var/www/html/vendor/laravel/lumen-framework/src/Concerns/RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch()
#9 /var/www/html/public/index.php(28): Laravel\\Lumen\\Application->run()
#10 {main}
"} 
