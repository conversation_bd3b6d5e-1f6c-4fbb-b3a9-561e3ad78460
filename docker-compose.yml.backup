services:

  mongodb:
    container_name: mongodb
    image: mongo:7.0
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-adminpassword}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-rapidsgspro}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network

  backend:
    container_name: backend
    build: .
    volumes:
      - ./html/backend:/var/www/html
    environment:
      - DB_CONNECTION=mongodb
      - DB_HOST=mongodb
      - DB_PORT=27017
      - DB_DATABASE=${MONGO_DATABASE:-rapidsgspro}
      - DB_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - DB_PASSWORD=${MONGO_ROOT_PASSWORD:-adminpassword}
    depends_on:
      - mongodb
    networks:
      - app-network

  frontend:
    container_name: frontend
    build:
      context: ./html/frontend
      dockerfile: Dockerfile
    volumes:
      - ./html/frontend:/app
      - frontend_node_modules:/app/node_modules
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://localhost/api
    networks:
      - app-network
    command: npm run dev -- --host 0.0.0.0

  nginx:
    container_name: nginx
    image: nginx:stable-alpine
    ports:
      - "${PORT:-80}:80"
    volumes:
      - ./html:/var/www/html
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
      - frontend
    networks:
      - app-network

volumes:
  mongodb_data:
  frontend_node_modules:

networks:
  app-network:
    driver: bridge