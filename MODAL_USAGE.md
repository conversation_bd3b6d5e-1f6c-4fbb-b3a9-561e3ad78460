# Система модальных окон RapidSGS Pro

Гибкая система модальных окон для Laravel Lumen + Alpine.js, предоставляющая множество компонентов и возможностей для создания интерактивных пользовательских интерфейсов.

## Основные компоненты

### 1. Базовый компонент модального окна (`x-modal`)

Основной компонент для создания любых модальных окон.

```blade
<x-modal 
    id="myModal" 
    title="Заголовок модального окна"
    size="md"
    animation="fade"
    closable="true"
    backdrop="true"
    centered="true">
    
    <!-- Содержимое модального окна -->
    <p>Здесь может быть любой контент</p>
    
    <!-- Опциональный пользовательский заголовок -->
    <x-slot name="header">
        <h3 class="text-lg font-medium">Пользовательский заголовок</h3>
    </x-slot>
    
    <!-- Опциональный футер -->
    <x-slot name="footer">
        <div class="flex justify-end space-x-3">
            <button @click="$dispatch('close-modal-myModal')">Закрыть</button>
        </div>
    </x-slot>
</x-modal>
```

#### Параметры:
- `id` - уникальный идентификатор модального окна
- `size` - размер модального окна (`sm`, `md`, `lg`, `xl`, `2xl`, `3xl`, `4xl`, `full`)
- `title` - заголовок модального окна
- `closable` - показывать ли кнопку закрытия (по умолчанию `true`)
- `backdrop` - показывать ли фон затемнения (по умолчанию `true`)
- `centered` - центрировать ли модальное окно (по умолчанию `true`)
- `animation` - тип анимации (`fade`, `slide`, `zoom`)
- `maxWidth` - пользовательская максимальная ширина

### 2. Модальное окно подтверждения (`x-confirm-modal`)

Специализированный компонент для подтверждения действий.

```blade
<x-confirm-modal 
    id="deleteModal"
    title="Удалить пользователя"
    message="Вы уверены, что хотите удалить этого пользователя?"
    confirm-text="Удалить"
    cancel-text="Отмена"
    confirm-class="bg-red-600 hover:bg-red-700 text-white"
    size="md"
    x-on:modal-confirmed-delete-modal.window="handleDelete()" />
```

#### Параметры:
- `message` - текст сообщения
- `confirm-text` - текст кнопки подтверждения
- `cancel-text` - текст кнопки отмены
- `confirm-class` - CSS классы для кнопки подтверждения
- `cancel-class` - CSS классы для кнопки отмены

### 3. Модальное окно с формой (`x-form-modal`)

Компонент для модальных окон с формами.

```blade
<x-form-modal 
    id="userForm" 
    title="Добавить пользователя"
    submit-text="Создать"
    cancel-text="Отмена"
    size="lg"
    action="/api/users"
    method="POST"
    form-id="addUserForm">
    
    <div class="space-y-4">
        <div>
            <label for="name">Имя</label>
            <input type="text" id="name" name="name" required>
        </div>
        <div>
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
        </div>
    </div>
</x-form-modal>
```

#### Параметры:
- `submit-text` - текст кнопки отправки
- `action` - URL для отправки формы
- `method` - HTTP метод (`GET`, `POST`, `PUT`, `DELETE`)
- `form-id` - ID формы (автоматически генерируется, если не указан)

## JavaScript API

### Основные функции

```javascript
// Открыть модальное окно
openModal('myModal');

// Закрыть модальное окно
closeModal('myModal');

// Переключить модальное окно
toggleModal('myModal');

// Закрыть все модальные окна
closeAllModals();
```

### Динамическое создание модальных окон

#### Модальное окно подтверждения

```javascript
showConfirmModal({
    title: 'Удалить файл',
    message: 'Файл будет удален без возможности восстановления',
    confirmText: 'Удалить',
    cancelText: 'Отмена',
    confirmClass: 'bg-red-600 hover:bg-red-700 text-white',
    onConfirm: () => {
        // Логика подтверждения
        console.log('Файл удален');
    },
    onCancel: () => {
        // Логика отмены
        console.log('Удаление отменено');
    }
});
```

#### Информационное модальное окно

```javascript
showInfoModal({
    title: 'Информация',
    content: `
        <div class="text-center">
            <p>Операция выполнена успешно!</p>
        </div>
    `,
    size: 'md',
    onClose: () => {
        console.log('Модальное окно закрыто');
    }
});
```

## События Alpine.js

### Открытие и закрытие модальных окон

```javascript
// Открыть модальное окно
$dispatch('open-modal-myModal');

// Закрыть модальное окно
$dispatch('close-modal-myModal');

// Слушать события подтверждения
x-on:modal-confirmed-myModal.window="handleConfirm()"
```

### Обработка событий в компонентах

```javascript
function myComponent() {
    return {
        init() {
            // Слушать события модального окна
            this.$el.addEventListener('modal-confirmed-deleteModal', () => {
                this.deleteItem();
            });
        },
        
        deleteItem() {
            // Логика удаления
        }
    }
}
```

## Стилизация и анимации

### Размеры модальных окон

- `sm` - `max-w-sm` (384px)
- `md` - `max-w-md` (448px)
- `lg` - `max-w-lg` (512px)
- `xl` - `max-w-xl` (576px)
- `2xl` - `max-w-2xl` (672px)
- `3xl` - `max-w-3xl` (768px)
- `4xl` - `max-w-4xl` (896px)
- `full` - `max-w-full`

### Типы анимаций

1. **Fade** (`fade`) - плавное появление с изменением прозрачности и масштаба
2. **Slide** (`slide`) - появление сверху с вертикальным смещением
3. **Zoom** (`zoom`) - увеличение от центра

### Пользовательские стили

Вы можете переопределить стили модальных окон через CSS:

```css
/* Пользовательские анимации */
.modal-custom-enter { 
    transition: opacity 300ms ease-out, transform 300ms ease-out; 
}
.modal-custom-enter-start { 
    opacity: 0; 
    transform: rotateX(-90deg); 
}
.modal-custom-enter-end { 
    opacity: 1; 
    transform: rotateX(0); 
}
```

## Примеры использования

### 1. Простое информационное модальное окно

```blade
<!-- Кнопка для открытия -->
<button @click="$dispatch('open-modal-infoModal')">
    Показать информацию
</button>

<!-- Модальное окно -->
<x-modal id="infoModal" title="Информация" size="md">
    <p>Это простое информационное сообщение.</p>
    
    <x-slot name="footer">
        <button @click="$dispatch('close-modal-infoModal')" 
                class="px-4 py-2 bg-blue-600 text-white rounded">
            Понятно
        </button>
    </x-slot>
</x-modal>
```

### 2. Форма редактирования пользователя

```blade
<x-form-modal 
    id="editUserModal" 
    title="Редактировать пользователя"
    submit-text="Сохранить"
    action="/api/users/update"
    method="PUT">
    
    <input type="hidden" x-model="editingUser.id" name="id">
    
    <div class="grid grid-cols-2 gap-4">
        <div>
            <label>Имя</label>
            <input type="text" x-model="editingUser.name" name="name" required>
        </div>
        <div>
            <label>Email</label>
            <input type="email" x-model="editingUser.email" name="email" required>
        </div>
    </div>
</x-form-modal>
```

### 3. Подтверждение удаления с динамическими данными

```javascript
function confirmDelete(item) {
    showConfirmModal({
        title: `Удалить ${item.type}`,
        message: `Вы уверены, что хотите удалить "${item.name}"? Это действие нельзя отменить.`,
        confirmText: 'Удалить',
        cancelText: 'Отмена',
        confirmClass: 'bg-red-600 hover:bg-red-700 text-white',
        onConfirm: async () => {
            try {
                await fetch(`/api/${item.type}/${item.id}`, { method: 'DELETE' });
                showInfoModal({
                    title: 'Успешно',
                    content: `${item.type} "${item.name}" удален.`
                });
                this.loadItems(); // Обновить список
            } catch (error) {
                showInfoModal({
                    title: 'Ошибка',
                    content: 'Произошла ошибка при удалении.'
                });
            }
        }
    });
}
```

### 4. Множественные модальные окна на одной странице

```blade
<!-- Модальное окно настроек -->
<x-modal id="settingsModal" title="Настройки" size="lg">
    <!-- Содержимое настроек -->
</x-modal>

<!-- Модальное окно справки -->
<x-modal id="helpModal" title="Справка" size="xl">
    <!-- Содержимое справки -->
</x-modal>

<!-- Модальное окно подтверждения выхода -->
<x-confirm-modal 
    id="logoutModal"
    title="Выход из системы"
    message="Вы действительно хотите выйти?"
    confirm-text="Выйти"
    x-on:modal-confirmed-logout-modal.window="logout()" />
```

## Лучшие практики

1. **Уникальные ID**: Всегда используйте уникальные идентификаторы для модальных окон
2. **Управление состоянием**: Используйте Alpine.js для управления данными модальных окон
3. **Accessibility**: Модальные окна автоматически поддерживают закрытие по Escape
4. **События**: Используйте систему событий для связи между компонентами
5. **Производительность**: Динамические модальные окна автоматически удаляются после закрытия
6. **Валидация**: Используйте HTML5 валидацию и Alpine.js для проверки форм

## Troubleshooting

### Модальное окно не открывается
- Проверьте правильность ID модального окна
- Убедитесь, что Alpine.js инициализирован
- Проверьте консоль браузера на наличие ошибок

### Анимации не работают
- Убедитесь, что стили CSS загружены
- Проверьте совместимость с Tailwind CSS
- Убедитесь, что используется правильный тип анимации

### События не срабатывают
- Проверьте правильность названий событий
- Убедитесь, что обработчики событий зарегистрированы
- Используйте `.window` модификатор для глобальных событий

### Формы не отправляются
- Проверьте атрибуты `action` и `method`
- Убедитесь, что CSRF токен добавлен
- Проверьте валидность HTML формы 