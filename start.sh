#!/bin/bash

echo "🚀 Starting RapidSGS Pro Development Environment..."

# Check if <PERSON><PERSON> is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Build and start containers
echo "📦 Building and starting containers..."
docker-compose up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Install backend dependencies
echo "📚 Installing backend dependencies..."
docker-compose exec -T backend composer install --no-interaction

# Install frontend dependencies  
echo "🎨 Installing frontend dependencies..."
docker-compose exec -T frontend npm install

echo ""
echo "✅ RapidSGS Pro is starting up!"
echo ""
echo "🌐 Frontend: https://rapid5gs.com.local"
echo "🔧 API: https://rapid5gs.com.local/api"
echo "📊 MongoDB: localhost:27017"
echo ""
echo "📝 Check logs with: docker-compose logs -f"
echo "🛑 Stop with: docker-compose down"
echo ""
echo "🔐 Create admin user by POST to /api/auth/register:"
echo '   {"name":"Admin","email":"<EMAIL>","password":"password123"}'