{"name": "rapidsgspro/backend", "description": "RapidSGS Pro Backend API", "type": "project", "require": {"php": "^8.2", "firebase/php-jwt": "^6.0", "illuminate/redis": "^10.0", "laravel/lumen-framework": "^10.0", "mongodb/laravel-mongodb": "^5.4", "mongodb/mongodb": "^2.1", "predis/predis": "^2.0"}, "require-dev": {"phpunit/phpunit": "^10.0", "mockery/mockery": "^1.4.4", "fakerphp/faker": "^1.9.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}