# RapidSGS Pro - Refactored Application

Приложение было полностью рефакторено из SPA архитектуры в server-rendered приложение с использованием Alpine.js и Blade шаблонов.

## Архитектура

- **Backend**: Lumen Framework (Laravel micro-framework)
- **Frontend**: Alpine.js + Blade Templates
- **Styling**: Tailwind CSS
- **Database**: MongoDB
- **Build Tool**: Vite

## Установка и настройка

### 1. Установка зависимостей

```bash
# Установка PHP зависимостей
composer install

# Установка Node.js зависимостей
npm install
```

### 2. Настройка окружения

```bash
# Скопировать файл окружения
cp .env.example .env

# Настроить переменные в .env файле
# Особенно важно настроить:
# - DB_CONNECTION=mongodb
# - DB_DATABASE=rapid5gs
# - APP_KEY (можно сгенерировать)
```

### 3. Подготовка директорий

```bash
# Создать директории для компилированных view
mkdir -p storage/framework/views
mkdir -p storage/logs

# Установить правильные права доступа
chmod -R 755 storage
```

## Запуск приложения

### Development режим (с Docker):
```bash
# Автоматический запуск через deploy скрипт
./deploy.sh development

# Или вручную
docker-compose up -d --build

# Приложение будет доступно по адресу:
# http://rapid5gs.com.local:8022
# Vite HMR: http://localhost:5173
```

### Development режим (без Docker):
```bash
# Установить зависимости
composer install
npm install

# Запуск Vite dev server (в отдельном терминале)
npm run dev

# Запуск PHP приложения
php -S localhost:8000 -t public
```

### Production режим (без Docker):
```bash
# Автоматический деплой
./deploy.sh production

# Или вручную
composer install --no-dev --optimize-autoloader
npm ci
npm run build

# Настроить веб-сервер для обслуживания public/ директории
# См. PRODUCTION.md для подробных инструкций
```

## Структура проекта

```
www/
├── app/                          # Backend логика
│   ├── Http/Controllers/         # Контроллеры (view + API)
│   └── Models/                   # MongoDB модели
├── config/                       # Конфигурационные файлы
├── public/                       # Публичные файлы и assets
├── resources/                    # Frontend ресурсы
│   ├── css/app.css              # Основные стили
│   ├── js/                      # JavaScript файлы
│   │   ├── app.js               # Основной entry point
│   │   ├── api.js               # API утилиты
│   │   └── components/          # Alpine.js компоненты
│   └── views/                   # Blade шаблоны
│       ├── layout.blade.php     # Основной layout
│       ├── dashboard.blade.php  # Дашборд
│       ├── users.blade.php      # Управление пользователями
│       ├── plans.blade.php      # Тарифные планы
│       └── enbs.blade.php       # Управление eNB
├── routes/web.php               # Маршруты
├── storage/                     # Хранилище (логи, кеш, views)
├── docker-compose.yml           # Docker для development
├── nginx.dev.conf              # Nginx для development
├── nginx.conf                  # Nginx для production
├── deploy.sh                   # Скрипт деплоя
├── vite.config.js              # Конфигурация Vite
├── tailwind.config.js          # Конфигурация Tailwind
└── package.json                # Node.js зависимости
```

## Особенности архитектуры

### Blade Templates
- Серверный рендеринг HTML
- Компонентная структура с layout
- Интеграция с Vite для assets

### Alpine.js
- Реактивность на клиенте
- Компонентная архитектура
- Управление состоянием без сложных framework'ов

### API + Views
- Контроллеры поддерживают как рендер view, так и API endpoints
- API endpoints доступны по префиксу `/api`
- View routes отдают полные HTML страницы

### Vite Integration
- Hot reload в development режиме
- Автоматическая обработка CSS/JS
- Оптимизация для production

### Docker для Development
- Отдельные контейнеры для app, MongoDB, Vite
- Hot reload поддерживается через volume mapping
- Nginx настроен для проксирования к PHP и Vite

## Development Workflow

### С Docker:
1. **Запустить через скрипт:**
   ```bash
   ./deploy.sh development
   ```

2. **Приложение будет доступно:**
   - Main app: http://rapid5gs.com.local:8022
   - Vite HMR: http://localhost:5173

### Без Docker:
1. **Запустить dev servers:**
   ```bash
   npm run dev  # Terminal 1
   php -S localhost:8000 -t public  # Terminal 2
   ```

2. **Редактировать файлы:**
   - Blade templates в `resources/views/`
   - CSS в `resources/css/`
   - JavaScript в `resources/js/`

3. **Hot reload:** Изменения автоматически применяются

## Production Deployment

**Важно**: В продакшене приложение работает БЕЗ Docker!

1. **Собрать ресурсы:**
   ```bash
   ./deploy.sh production
   ```

2. **Настроить веб-сервер** согласно `PRODUCTION.md`

3. **Настроить MongoDB** и права доступа

См. подробные инструкции в [PRODUCTION.md](PRODUCTION.md)

## API Endpoints

- `GET /` - Dashboard
- `GET /users` - Users management
- `GET /plans` - Plans management  
- `GET /enbs` - eNBs management
- `POST/PUT/DELETE /api/*` - REST API endpoints

## Технические детали

- **CSRF Protection:** Настроена для форм
- **Toast Notifications:** Alpine.js компонент
- **Loading States:** Встроены в компоненты
- **Error Handling:** Централизованная обработка
- **Responsive Design:** Полностью адаптивный интерфейс
- **MongoDB Integration:** Через Laravel MongoDB package
- **Security:** Блокировка доступа к служебным файлам