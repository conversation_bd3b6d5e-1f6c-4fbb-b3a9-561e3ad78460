services:

  mongodb-rapidsgspro:
    container_name: mongodb-rapidsgspro-prod
    image: mongo:7.0
    restart: unless-stopped
    ports:
      - "27017:27017"
    networks:
      - rapidsgspro-network
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

  backend-rapidsgspro:
    container_name: backend-rapidsgspro-prod
    build: .
    environment:
      - APP_URL=https://rapid5gs.com.local
      - APP_ENV=production
      - APP_DEBUG=false
    volumes:
      - ./html/backend:/var/www/html
    depends_on:
      - mongodb-rapidsgspro
    networks:
      - rapidsgspro-network

  # Build stage for frontend
  frontend-builder:
    image: node:18-alpine
    container_name: frontend-builder
    working_dir: /app
    command: sh -c "npm ci && npm run build"
    environment:
      - VITE_API_URL=https://rapid5gs.com.local/api
      - NODE_ENV=production
    volumes:
      - ./html/frontend:/app
      - frontend_dist:/app/dist
    networks:
      - rapidsgspro-network

  nginx-rapidsgspro:
    image: nginx:alpine
    container_name: nginx-rapidsgspro-prod
    restart: unless-stopped
    ports:
      - "8022:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - frontend_dist:/var/www/html/frontend
    depends_on:
      - backend-rapidsgspro
      - frontend-builder
    networks:
      - rapidsgspro-network

volumes:
  mongodb_data:
  frontend_dist:

networks:
  rapidsgspro-network:
    driver: bridge 