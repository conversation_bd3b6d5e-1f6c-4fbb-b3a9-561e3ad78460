var Le=!1,Ie=!1,R=[],je=-1;function Xn(e){Yn(e)}function Yn(e){R.includes(e)||R.push(e),Qn()}function Zn(e){let t=R.indexOf(e);t!==-1&&t>je&&R.splice(t,1)}function Qn(){!Ie&&!Le&&(Le=!0,queueMicrotask(er))}function er(){Le=!1,Ie=!0;for(let e=0;e<R.length;e++)R[e](),je=e;R.length=0,je=-1,Ie=!1}var V,H,G,It,Fe=!0;function tr(e){Fe=!1,e(),Fe=!0}function nr(e){V=e.reactive,G=e.release,H=t=>e.effect(t,{scheduler:n=>{Fe?Xn(n):n()}}),It=e.raw}function yt(e){H=e}function rr(e){let t=()=>{};return[r=>{let i=H(r);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(s=>s())}),e._x_effects.add(i),t=()=>{i!==void 0&&(e._x_effects.delete(i),G(i))},i},()=>{t()}]}function jt(e,t){let n=!0,r,i=H(()=>{let s=e();JSON.stringify(s),n?r=s:queueMicrotask(()=>{t(s,r),r=s}),n=!1});return()=>G(i)}var Ft=[],Nt=[],Rt=[];function ir(e){Rt.push(e)}function Xe(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,Nt.push(t))}function Bt(e){Ft.push(e)}function Dt(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function Ut(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([n,r])=>{(t===void 0||t.includes(n))&&(r.forEach(i=>i()),delete e._x_attributeCleanups[n])})}function sr(e){var t,n;for((t=e._x_effects)==null||t.forEach(Zn);(n=e._x_cleanups)!=null&&n.length;)e._x_cleanups.pop()()}var Ye=new MutationObserver(tt),Ze=!1;function Qe(){Ye.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Ze=!0}function zt(){or(),Ye.disconnect(),Ze=!1}var ee=[];function or(){let e=Ye.takeRecords();ee.push(()=>e.length>0&&tt(e));let t=ee.length;queueMicrotask(()=>{if(ee.length===t)for(;ee.length>0;)ee.shift()()})}function _(e){if(!Ze)return e();zt();let t=e();return Qe(),t}var et=!1,_e=[];function ar(){et=!0}function lr(){et=!1,tt(_e),_e=[]}function tt(e){if(et){_e=_e.concat(e);return}let t=[],n=new Set,r=new Map,i=new Map;for(let s=0;s<e.length;s++)if(!e[s].target._x_ignoreMutationObserver&&(e[s].type==="childList"&&(e[s].removedNodes.forEach(o=>{o.nodeType===1&&o._x_marker&&n.add(o)}),e[s].addedNodes.forEach(o=>{if(o.nodeType===1){if(n.has(o)){n.delete(o);return}o._x_marker||t.push(o)}})),e[s].type==="attributes")){let o=e[s].target,a=e[s].attributeName,l=e[s].oldValue,c=()=>{r.has(o)||r.set(o,[]),r.get(o).push({name:a,value:o.getAttribute(a)})},u=()=>{i.has(o)||i.set(o,[]),i.get(o).push(a)};o.hasAttribute(a)&&l===null?c():o.hasAttribute(a)?(u(),c()):u()}i.forEach((s,o)=>{Ut(o,s)}),r.forEach((s,o)=>{Ft.forEach(a=>a(o,s))});for(let s of n)t.some(o=>o.contains(s))||Nt.forEach(o=>o(s));for(let s of t)s.isConnected&&Rt.forEach(o=>o(s));t=null,n=null,r=null,i=null}function Ht(e){return le(W(e))}function ae(e,t,n){return e._x_dataStack=[t,...W(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(r=>r!==t)}}function W(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?W(e.host):e.parentNode?W(e.parentNode):[]}function le(e){return new Proxy({objects:e},cr)}var cr={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(n=>Object.prototype.hasOwnProperty.call(n,t)||Reflect.has(n,t))},get({objects:e},t,n){return t=="toJSON"?ur:Reflect.get(e.find(r=>Reflect.has(r,t))||{},t,n)},set({objects:e},t,n,r){const i=e.find(o=>Object.prototype.hasOwnProperty.call(o,t))||e[e.length-1],s=Object.getOwnPropertyDescriptor(i,t);return s!=null&&s.set&&(s!=null&&s.get)?s.set.call(r,n)||!0:Reflect.set(i,t,n)}};function ur(){return Reflect.ownKeys(this).reduce((t,n)=>(t[n]=Reflect.get(this,n),t),{})}function qt(e){let t=r=>typeof r=="object"&&!Array.isArray(r)&&r!==null,n=(r,i="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([s,{value:o,enumerable:a}])=>{if(a===!1||o===void 0||typeof o=="object"&&o!==null&&o.__v_skip)return;let l=i===""?s:`${i}.${s}`;typeof o=="object"&&o!==null&&o._x_interceptor?r[s]=o.initialize(e,l,s):t(o)&&o!==r&&!(o instanceof Element)&&n(o,l)})};return n(e)}function Kt(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:!0,initialize(r,i,s){return e(this.initialValue,()=>dr(r,i),o=>Ne(r,i,o),i,s)}};return t(n),r=>{if(typeof r=="object"&&r!==null&&r._x_interceptor){let i=n.initialize.bind(n);n.initialize=(s,o,a)=>{let l=r.initialize(s,o,a);return n.initialValue=l,i(s,o,a)}}else n.initialValue=r;return n}}function dr(e,t){return t.split(".").reduce((n,r)=>n[r],e)}function Ne(e,t,n){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=n;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),Ne(e[t[0]],t.slice(1),n)}}var Wt={};function T(e,t){Wt[e]=t}function Re(e,t){let n=fr(t);return Object.entries(Wt).forEach(([r,i])=>{Object.defineProperty(e,`$${r}`,{get(){return i(t,n)},enumerable:!1})}),e}function fr(e){let[t,n]=Zt(e),r={interceptor:Kt,...t};return Xe(e,n),r}function hr(e,t,n,...r){try{return n(...r)}catch(i){oe(i,e,t)}}function oe(e,t,n=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

${n?'Expression: "'+n+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var ge=!0;function Jt(e){let t=ge;ge=!1;let n=e();return ge=t,n}function B(e,t,n={}){let r;return b(e,t)(i=>r=i,n),r}function b(...e){return Vt(...e)}var Vt=Gt;function pr(e){Vt=e}function Gt(e,t){let n={};Re(n,e);let r=[n,...W(e)],i=typeof t=="function"?gr(r,t):_r(r,t,e);return hr.bind(null,e,t,i)}function gr(e,t){return(n=()=>{},{scope:r={},params:i=[]}={})=>{let s=t.apply(le([r,...e]),i);ye(n,s)}}var Te={};function mr(e,t){if(Te[e])return Te[e];let n=Object.getPrototypeOf(async function(){}).constructor,r=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,s=(()=>{try{let o=new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`);return Object.defineProperty(o,"name",{value:`[Alpine] ${e}`}),o}catch(o){return oe(o,t,e),Promise.resolve()}})();return Te[e]=s,s}function _r(e,t,n){let r=mr(t,n);return(i=()=>{},{scope:s={},params:o=[]}={})=>{r.result=void 0,r.finished=!1;let a=le([s,...e]);if(typeof r=="function"){let l=r(r,a).catch(c=>oe(c,n,t));r.finished?(ye(i,r.result,a,o,n),r.result=void 0):l.then(c=>{ye(i,c,a,o,n)}).catch(c=>oe(c,n,t)).finally(()=>r.result=void 0)}}}function ye(e,t,n,r,i){if(ge&&typeof t=="function"){let s=t.apply(n,r);s instanceof Promise?s.then(o=>ye(e,o,n,r)).catch(o=>oe(o,i,t)):e(s)}else typeof t=="object"&&t instanceof Promise?t.then(s=>e(s)):e(t)}var nt="x-";function X(e=""){return nt+e}function yr(e){nt=e}var we={};function w(e,t){return we[e]=t,{before(n){if(!we[n]){console.warn(String.raw`Cannot find directive \`${n}\`. \`${e}\` will use the default order of execution`);return}const r=N.indexOf(n);N.splice(r>=0?r:N.indexOf("DEFAULT"),0,e)}}}function wr(e){return Object.keys(we).includes(e)}function rt(e,t,n){if(t=Array.from(t),e._x_virtualDirectives){let s=Object.entries(e._x_virtualDirectives).map(([a,l])=>({name:a,value:l})),o=Xt(s);s=s.map(a=>o.find(l=>l.name===a.name)?{name:`x-bind:${a.name}`,value:`"${a.value}"`}:a),t=t.concat(s)}let r={};return t.map(tn((s,o)=>r[s]=o)).filter(rn).map(br(r,n)).sort(Er).map(s=>xr(e,s))}function Xt(e){return Array.from(e).map(tn()).filter(t=>!rn(t))}var Be=!1,re=new Map,Yt=Symbol();function vr(e){Be=!0;let t=Symbol();Yt=t,re.set(t,[]);let n=()=>{for(;re.get(t).length;)re.get(t).shift()();re.delete(t)},r=()=>{Be=!1,n()};e(n),r()}function Zt(e){let t=[],n=a=>t.push(a),[r,i]=rr(e);return t.push(i),[{Alpine:ce,effect:r,cleanup:n,evaluateLater:b.bind(b,e),evaluate:B.bind(B,e)},()=>t.forEach(a=>a())]}function xr(e,t){let n=()=>{},r=we[t.type]||n,[i,s]=Zt(e);Dt(e,t.original,s);let o=()=>{e._x_ignore||e._x_ignoreSelf||(r.inline&&r.inline(e,t,i),r=r.bind(r,e,t,i),Be?re.get(Yt).push(r):r())};return o.runCleanups=s,o}var Qt=(e,t)=>({name:n,value:r})=>(n.startsWith(e)&&(n=n.replace(e,t)),{name:n,value:r}),en=e=>e;function tn(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:i}=nn.reduce((s,o)=>o(s),{name:t,value:n});return r!==t&&e(r,t),{name:r,value:i}}}var nn=[];function it(e){nn.push(e)}function rn({name:e}){return sn().test(e)}var sn=()=>new RegExp(`^${nt}([^:^.]+)\\b`);function br(e,t){return({name:n,value:r})=>{let i=n.match(sn()),s=n.match(/:([a-zA-Z0-9\-_:]+)/),o=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],a=t||e[n]||n;return{type:i?i[1]:null,value:s?s[1]:null,modifiers:o.map(l=>l.replace(".","")),expression:r,original:a}}}var De="DEFAULT",N=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",De,"teleport"];function Er(e,t){let n=N.indexOf(e.type)===-1?De:e.type,r=N.indexOf(t.type)===-1?De:t.type;return N.indexOf(n)-N.indexOf(r)}function ie(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}function z(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(i=>z(i,t));return}let n=!1;if(t(e,()=>n=!0),n)return;let r=e.firstElementChild;for(;r;)z(r,t),r=r.nextElementSibling}function A(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var wt=!1;function Sr(){wt&&A("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),wt=!0,document.body||A("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),ie(document,"alpine:init"),ie(document,"alpine:initializing"),Qe(),ir(t=>k(t,z)),Xe(t=>Z(t)),Bt((t,n)=>{rt(t,n).forEach(r=>r())});let e=t=>!xe(t.parentElement,!0);Array.from(document.querySelectorAll(ln().join(","))).filter(e).forEach(t=>{k(t)}),ie(document,"alpine:initialized"),setTimeout(()=>{Tr()})}var st=[],on=[];function an(){return st.map(e=>e())}function ln(){return st.concat(on).map(e=>e())}function cn(e){st.push(e)}function un(e){on.push(e)}function xe(e,t=!1){return Y(e,n=>{if((t?ln():an()).some(i=>n.matches(i)))return!0})}function Y(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return Y(e.parentElement,t)}}function Ar(e){return an().some(t=>e.matches(t))}var dn=[];function Mr(e){dn.push(e)}var Cr=1;function k(e,t=z,n=()=>{}){Y(e,r=>r._x_ignore)||vr(()=>{t(e,(r,i)=>{r._x_marker||(n(r,i),dn.forEach(s=>s(r,i)),rt(r,r.attributes).forEach(s=>s()),r._x_ignore||(r._x_marker=Cr++),r._x_ignore&&i())})})}function Z(e,t=z){t(e,n=>{sr(n),Ut(n),delete n._x_marker})}function Tr(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,n,r])=>{wr(n)||r.some(i=>{if(document.querySelector(i))return A(`found "${i}", but missing ${t} plugin`),!0})})}var Ue=[],ot=!1;function at(e=()=>{}){return queueMicrotask(()=>{ot||setTimeout(()=>{ze()})}),new Promise(t=>{Ue.push(()=>{e(),t()})})}function ze(){for(ot=!1;Ue.length;)Ue.shift()()}function Or(){ot=!0}function lt(e,t){return Array.isArray(t)?vt(e,t.join(" ")):typeof t=="object"&&t!==null?$r(e,t):typeof t=="function"?lt(e,t()):vt(e,t)}function vt(e,t){let n=i=>i.split(" ").filter(s=>!e.classList.contains(s)).filter(Boolean),r=i=>(e.classList.add(...i),()=>{e.classList.remove(...i)});return t=t===!0?t="":t||"",r(n(t))}function $r(e,t){let n=a=>a.split(" ").filter(Boolean),r=Object.entries(t).flatMap(([a,l])=>l?n(a):!1).filter(Boolean),i=Object.entries(t).flatMap(([a,l])=>l?!1:n(a)).filter(Boolean),s=[],o=[];return i.forEach(a=>{e.classList.contains(a)&&(e.classList.remove(a),o.push(a))}),r.forEach(a=>{e.classList.contains(a)||(e.classList.add(a),s.push(a))}),()=>{o.forEach(a=>e.classList.add(a)),s.forEach(a=>e.classList.remove(a))}}function be(e,t){return typeof t=="object"&&t!==null?kr(e,t):Pr(e,t)}function kr(e,t){let n={};return Object.entries(t).forEach(([r,i])=>{n[r]=e.style[r],r.startsWith("--")||(r=Lr(r)),e.style.setProperty(r,i)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{be(e,n)}}function Pr(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function Lr(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function He(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}w("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:i})=>{typeof r=="function"&&(r=i(r)),r!==!1&&(!r||typeof r=="boolean"?jr(e,n,t):Ir(e,r,t))});function Ir(e,t,n){fn(e,lt,""),{enter:i=>{e._x_transition.enter.during=i},"enter-start":i=>{e._x_transition.enter.start=i},"enter-end":i=>{e._x_transition.enter.end=i},leave:i=>{e._x_transition.leave.during=i},"leave-start":i=>{e._x_transition.leave.start=i},"leave-end":i=>{e._x_transition.leave.end=i}}[n](t)}function jr(e,t,n){fn(e,be);let r=!t.includes("in")&&!t.includes("out")&&!n,i=r||t.includes("in")||["enter"].includes(n),s=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter((p,m)=>m<t.indexOf("out"))),t.includes("out")&&!r&&(t=t.filter((p,m)=>m>t.indexOf("out")));let o=!t.includes("opacity")&&!t.includes("scale"),a=o||t.includes("opacity"),l=o||t.includes("scale"),c=a?0:1,u=l?te(t,"scale",95)/100:1,f=te(t,"delay",0)/1e3,h=te(t,"origin","center"),y="opacity, transform",M=te(t,"duration",150)/1e3,$=te(t,"duration",75)/1e3,d="cubic-bezier(0.4, 0.0, 0.2, 1)";i&&(e._x_transition.enter.during={transformOrigin:h,transitionDelay:`${f}s`,transitionProperty:y,transitionDuration:`${M}s`,transitionTimingFunction:d},e._x_transition.enter.start={opacity:c,transform:`scale(${u})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),s&&(e._x_transition.leave.during={transformOrigin:h,transitionDelay:`${f}s`,transitionProperty:y,transitionDuration:`${$}s`,transitionTimingFunction:d},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:c,transform:`scale(${u})`})}function fn(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(r=()=>{},i=()=>{}){qe(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},r,i)},out(r=()=>{},i=()=>{}){qe(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},r,i)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){const i=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let s=()=>i(n);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):s():e._x_transition?e._x_transition.in(n):s();return}e._x_hidePromise=e._x_transition?new Promise((o,a)=>{e._x_transition.out(()=>{},()=>o(r)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>a({isFromCancelledTransition:!0}))}):Promise.resolve(r),queueMicrotask(()=>{let o=hn(e);o?(o._x_hideChildren||(o._x_hideChildren=[]),o._x_hideChildren.push(e)):i(()=>{let a=l=>{let c=Promise.all([l._x_hidePromise,...(l._x_hideChildren||[]).map(a)]).then(([u])=>u==null?void 0:u());return delete l._x_hidePromise,delete l._x_hideChildren,c};a(e).catch(l=>{if(!l.isFromCancelledTransition)throw l})})})};function hn(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:hn(t)}function qe(e,t,{during:n,start:r,end:i}={},s=()=>{},o=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(i).length===0){s(),o();return}let a,l,c;Fr(e,{start(){a=t(e,r)},during(){l=t(e,n)},before:s,end(){a(),c=t(e,i)},after:o,cleanup(){l(),c()}})}function Fr(e,t){let n,r,i,s=He(()=>{_(()=>{n=!0,r||t.before(),i||(t.end(),ze()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(o){this.beforeCancels.push(o)},cancel:He(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();s()}),finish:s},_(()=>{t.start(),t.during()}),Or(),requestAnimationFrame(()=>{if(n)return;let o=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,a=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;o===0&&(o=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),_(()=>{t.before()}),r=!0,requestAnimationFrame(()=>{n||(_(()=>{t.end()}),ze(),setTimeout(e._x_transitioning.finish,o+a),i=!0)})})}function te(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r||t==="scale"&&isNaN(r))return n;if(t==="duration"||t==="delay"){let i=r.match(/([0-9]+)ms/);if(i)return i[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var L=!1;function j(e,t=()=>{}){return(...n)=>L?t(...n):e(...n)}function Nr(e){return(...t)=>L&&e(...t)}var pn=[];function Ee(e){pn.push(e)}function Rr(e,t){pn.forEach(n=>n(e,t)),L=!0,gn(()=>{k(t,(n,r)=>{r(n,()=>{})})}),L=!1}var Ke=!1;function Br(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),L=!0,Ke=!0,gn(()=>{Dr(t)}),L=!1,Ke=!1}function Dr(e){let t=!1;k(e,(r,i)=>{z(r,(s,o)=>{if(t&&Ar(s))return o();t=!0,i(s,o)})})}function gn(e){let t=H;yt((n,r)=>{let i=t(n);return G(i),()=>{}}),e(),yt(t)}function mn(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=V({})),e._x_bindings[t]=n,t=r.includes("camel")?Vr(t):t,t){case"value":Ur(e,n);break;case"style":Hr(e,n);break;case"class":zr(e,n);break;case"selected":case"checked":qr(e,t,n);break;default:_n(e,t,n);break}}function Ur(e,t){if(vn(e))e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=me(e.value)===t:e.checked=xt(e.value,t));else if(ct(e))Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(n=>xt(n,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")Jr(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function zr(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=lt(e,t)}function Hr(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=be(e,t)}function qr(e,t,n){_n(e,t,n),Wr(e,t,n)}function _n(e,t,n){[null,void 0,!1].includes(n)&&Xr(t)?e.removeAttribute(t):(yn(t)&&(n=t),Kr(e,t,n))}function Kr(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function Wr(e,t,n){e[t]!==n&&(e[t]=n)}function Jr(e,t){const n=[].concat(t).map(r=>r+"");Array.from(e.options).forEach(r=>{r.selected=n.includes(r.value)})}function Vr(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function xt(e,t){return e==t}function me(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}var Gr=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function yn(e){return Gr.has(e)}function Xr(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function Yr(e,t,n){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:wn(e,t,n)}function Zr(e,t,n,r=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let i=e._x_inlineBindings[t];return i.extract=r,Jt(()=>B(e,i.expression))}return wn(e,t,n)}function wn(e,t,n){let r=e.getAttribute(t);return r===null?typeof n=="function"?n():n:r===""?!0:yn(t)?!![t,"true"].includes(r):r}function ct(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function vn(e){return e.type==="radio"||e.localName==="ui-radio"}function xn(e,t){var n;return function(){var r=this,i=arguments,s=function(){n=null,e.apply(r,i)};clearTimeout(n),n=setTimeout(s,t)}}function bn(e,t){let n;return function(){let r=this,i=arguments;n||(e.apply(r,i),n=!0,setTimeout(()=>n=!1,t))}}function En({get:e,set:t},{get:n,set:r}){let i=!0,s,o=H(()=>{let a=e(),l=n();if(i)r(Oe(a)),i=!1;else{let c=JSON.stringify(a),u=JSON.stringify(l);c!==s?r(Oe(a)):c!==u&&t(Oe(l))}s=JSON.stringify(e()),JSON.stringify(n())});return()=>{G(o)}}function Oe(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function Qr(e){(Array.isArray(e)?e:[e]).forEach(n=>n(ce))}var F={},bt=!1;function ei(e,t){if(bt||(F=V(F),bt=!0),t===void 0)return F[e];F[e]=t,qt(F[e]),typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&F[e].init()}function ti(){return F}var Sn={};function ni(e,t){let n=typeof t!="function"?()=>t:t;return e instanceof Element?An(e,n()):(Sn[e]=n,()=>{})}function ri(e){return Object.entries(Sn).forEach(([t,n])=>{Object.defineProperty(e,t,{get(){return(...r)=>n(...r)}})}),e}function An(e,t,n){let r=[];for(;r.length;)r.pop()();let i=Object.entries(t).map(([o,a])=>({name:o,value:a})),s=Xt(i);return i=i.map(o=>s.find(a=>a.name===o.name)?{name:`x-bind:${o.name}`,value:`"${o.value}"`}:o),rt(e,i,n).map(o=>{r.push(o.runCleanups),o()}),()=>{for(;r.length;)r.pop()()}}var Mn={};function ii(e,t){Mn[e]=t}function si(e,t){return Object.entries(Mn).forEach(([n,r])=>{Object.defineProperty(e,n,{get(){return(...i)=>r.bind(t)(...i)},enumerable:!1})}),e}var oi={get reactive(){return V},get release(){return G},get effect(){return H},get raw(){return It},version:"3.14.9",flushAndStopDeferringMutations:lr,dontAutoEvaluateFunctions:Jt,disableEffectScheduling:tr,startObservingMutations:Qe,stopObservingMutations:zt,setReactivityEngine:nr,onAttributeRemoved:Dt,onAttributesAdded:Bt,closestDataStack:W,skipDuringClone:j,onlyDuringClone:Nr,addRootSelector:cn,addInitSelector:un,interceptClone:Ee,addScopeToNode:ae,deferMutations:ar,mapAttributes:it,evaluateLater:b,interceptInit:Mr,setEvaluator:pr,mergeProxies:le,extractProp:Zr,findClosest:Y,onElRemoved:Xe,closestRoot:xe,destroyTree:Z,interceptor:Kt,transition:qe,setStyles:be,mutateDom:_,directive:w,entangle:En,throttle:bn,debounce:xn,evaluate:B,initTree:k,nextTick:at,prefixed:X,prefix:yr,plugin:Qr,magic:T,store:ei,start:Sr,clone:Br,cloneNode:Rr,bound:Yr,$data:Ht,watch:jt,walk:z,data:ii,bind:ni},ce=oi;function ai(e,t){const n=Object.create(null),r=e.split(",");for(let i=0;i<r.length;i++)n[r[i]]=!0;return t?i=>!!n[i.toLowerCase()]:i=>!!n[i]}var li=Object.freeze({}),ci=Object.prototype.hasOwnProperty,Se=(e,t)=>ci.call(e,t),D=Array.isArray,se=e=>Cn(e)==="[object Map]",ui=e=>typeof e=="string",ut=e=>typeof e=="symbol",Ae=e=>e!==null&&typeof e=="object",di=Object.prototype.toString,Cn=e=>di.call(e),Tn=e=>Cn(e).slice(8,-1),dt=e=>ui(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,fi=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},hi=fi(e=>e.charAt(0).toUpperCase()+e.slice(1)),On=(e,t)=>e!==t&&(e===e||t===t),We=new WeakMap,ne=[],O,U=Symbol("iterate"),Je=Symbol("Map key iterate");function pi(e){return e&&e._isEffect===!0}function gi(e,t=li){pi(e)&&(e=e.raw);const n=yi(e,t);return t.lazy||n(),n}function mi(e){e.active&&($n(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var _i=0;function yi(e,t){const n=function(){if(!n.active)return e();if(!ne.includes(n)){$n(n);try{return vi(),ne.push(n),O=n,e()}finally{ne.pop(),kn(),O=ne[ne.length-1]}}};return n.id=_i++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}function $n(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var J=!0,ft=[];function wi(){ft.push(J),J=!1}function vi(){ft.push(J),J=!0}function kn(){const e=ft.pop();J=e===void 0?!0:e}function C(e,t,n){if(!J||O===void 0)return;let r=We.get(e);r||We.set(e,r=new Map);let i=r.get(n);i||r.set(n,i=new Set),i.has(O)||(i.add(O),O.deps.push(i),O.options.onTrack&&O.options.onTrack({effect:O,target:e,type:t,key:n}))}function I(e,t,n,r,i,s){const o=We.get(e);if(!o)return;const a=new Set,l=u=>{u&&u.forEach(f=>{(f!==O||f.allowRecurse)&&a.add(f)})};if(t==="clear")o.forEach(l);else if(n==="length"&&D(e))o.forEach((u,f)=>{(f==="length"||f>=r)&&l(u)});else switch(n!==void 0&&l(o.get(n)),t){case"add":D(e)?dt(n)&&l(o.get("length")):(l(o.get(U)),se(e)&&l(o.get(Je)));break;case"delete":D(e)||(l(o.get(U)),se(e)&&l(o.get(Je)));break;case"set":se(e)&&l(o.get(U));break}const c=u=>{u.options.onTrigger&&u.options.onTrigger({effect:u,target:e,key:n,type:t,newValue:r,oldValue:i,oldTarget:s}),u.options.scheduler?u.options.scheduler(u):u()};a.forEach(c)}var xi=ai("__proto__,__v_isRef,__isVue"),Pn=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(ut)),bi=Ln(),Ei=Ln(!0),Et=Si();function Si(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=g(this);for(let s=0,o=this.length;s<o;s++)C(r,"get",s+"");const i=r[t](...n);return i===-1||i===!1?r[t](...n.map(g)):i}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){wi();const r=g(this)[t].apply(this,n);return kn(),r}}),e}function Ln(e=!1,t=!1){return function(r,i,s){if(i==="__v_isReactive")return!e;if(i==="__v_isReadonly")return e;if(i==="__v_raw"&&s===(e?t?Di:Nn:t?Bi:Fn).get(r))return r;const o=D(r);if(!e&&o&&Se(Et,i))return Reflect.get(Et,i,s);const a=Reflect.get(r,i,s);return(ut(i)?Pn.has(i):xi(i))||(e||C(r,"get",i),t)?a:Ve(a)?!o||!dt(i)?a.value:a:Ae(a)?e?Rn(a):mt(a):a}}var Ai=Mi();function Mi(e=!1){return function(n,r,i,s){let o=n[r];if(!e&&(i=g(i),o=g(o),!D(n)&&Ve(o)&&!Ve(i)))return o.value=i,!0;const a=D(n)&&dt(r)?Number(r)<n.length:Se(n,r),l=Reflect.set(n,r,i,s);return n===g(s)&&(a?On(i,o)&&I(n,"set",r,i,o):I(n,"add",r,i)),l}}function Ci(e,t){const n=Se(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&I(e,"delete",t,void 0,r),i}function Ti(e,t){const n=Reflect.has(e,t);return(!ut(t)||!Pn.has(t))&&C(e,"has",t),n}function Oi(e){return C(e,"iterate",D(e)?"length":U),Reflect.ownKeys(e)}var $i={get:bi,set:Ai,deleteProperty:Ci,has:Ti,ownKeys:Oi},ki={get:Ei,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},ht=e=>Ae(e)?mt(e):e,pt=e=>Ae(e)?Rn(e):e,gt=e=>e,Me=e=>Reflect.getPrototypeOf(e);function ue(e,t,n=!1,r=!1){e=e.__v_raw;const i=g(e),s=g(t);t!==s&&!n&&C(i,"get",t),!n&&C(i,"get",s);const{has:o}=Me(i),a=r?gt:n?pt:ht;if(o.call(i,t))return a(e.get(t));if(o.call(i,s))return a(e.get(s));e!==i&&e.get(t)}function de(e,t=!1){const n=this.__v_raw,r=g(n),i=g(e);return e!==i&&!t&&C(r,"has",e),!t&&C(r,"has",i),e===i?n.has(e):n.has(e)||n.has(i)}function fe(e,t=!1){return e=e.__v_raw,!t&&C(g(e),"iterate",U),Reflect.get(e,"size",e)}function St(e){e=g(e);const t=g(this);return Me(t).has.call(t,e)||(t.add(e),I(t,"add",e,e)),this}function At(e,t){t=g(t);const n=g(this),{has:r,get:i}=Me(n);let s=r.call(n,e);s?jn(n,r,e):(e=g(e),s=r.call(n,e));const o=i.call(n,e);return n.set(e,t),s?On(t,o)&&I(n,"set",e,t,o):I(n,"add",e,t),this}function Mt(e){const t=g(this),{has:n,get:r}=Me(t);let i=n.call(t,e);i?jn(t,n,e):(e=g(e),i=n.call(t,e));const s=r?r.call(t,e):void 0,o=t.delete(e);return i&&I(t,"delete",e,void 0,s),o}function Ct(){const e=g(this),t=e.size!==0,n=se(e)?new Map(e):new Set(e),r=e.clear();return t&&I(e,"clear",void 0,void 0,n),r}function he(e,t){return function(r,i){const s=this,o=s.__v_raw,a=g(o),l=t?gt:e?pt:ht;return!e&&C(a,"iterate",U),o.forEach((c,u)=>r.call(i,l(c),l(u),s))}}function pe(e,t,n){return function(...r){const i=this.__v_raw,s=g(i),o=se(s),a=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,c=i[e](...r),u=n?gt:t?pt:ht;return!t&&C(s,"iterate",l?Je:U),{next(){const{value:f,done:h}=c.next();return h?{value:f,done:h}:{value:a?[u(f[0]),u(f[1])]:u(f),done:h}},[Symbol.iterator](){return this}}}}function P(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${hi(e)} operation ${n}failed: target is readonly.`,g(this))}return e==="delete"?!1:this}}function Pi(){const e={get(s){return ue(this,s)},get size(){return fe(this)},has:de,add:St,set:At,delete:Mt,clear:Ct,forEach:he(!1,!1)},t={get(s){return ue(this,s,!1,!0)},get size(){return fe(this)},has:de,add:St,set:At,delete:Mt,clear:Ct,forEach:he(!1,!0)},n={get(s){return ue(this,s,!0)},get size(){return fe(this,!0)},has(s){return de.call(this,s,!0)},add:P("add"),set:P("set"),delete:P("delete"),clear:P("clear"),forEach:he(!0,!1)},r={get(s){return ue(this,s,!0,!0)},get size(){return fe(this,!0)},has(s){return de.call(this,s,!0)},add:P("add"),set:P("set"),delete:P("delete"),clear:P("clear"),forEach:he(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=pe(s,!1,!1),n[s]=pe(s,!0,!1),t[s]=pe(s,!1,!0),r[s]=pe(s,!0,!0)}),[e,n,t,r]}var[Li,Ii,ji,Fi]=Pi();function In(e,t){const n=t?e?Fi:ji:e?Ii:Li;return(r,i,s)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get(Se(n,i)&&i in r?n:r,i,s)}var Ni={get:In(!1,!1)},Ri={get:In(!0,!1)};function jn(e,t,n){const r=g(n);if(r!==n&&t.call(e,r)){const i=Tn(e);console.warn(`Reactive ${i} contains both the raw and reactive versions of the same object${i==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var Fn=new WeakMap,Bi=new WeakMap,Nn=new WeakMap,Di=new WeakMap;function Ui(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function zi(e){return e.__v_skip||!Object.isExtensible(e)?0:Ui(Tn(e))}function mt(e){return e&&e.__v_isReadonly?e:Bn(e,!1,$i,Ni,Fn)}function Rn(e){return Bn(e,!0,ki,Ri,Nn)}function Bn(e,t,n,r,i){if(!Ae(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=i.get(e);if(s)return s;const o=zi(e);if(o===0)return e;const a=new Proxy(e,o===2?r:n);return i.set(e,a),a}function g(e){return e&&g(e.__v_raw)||e}function Ve(e){return!!(e&&e.__v_isRef===!0)}T("nextTick",()=>at);T("dispatch",e=>ie.bind(ie,e));T("watch",(e,{evaluateLater:t,cleanup:n})=>(r,i)=>{let s=t(r),a=jt(()=>{let l;return s(c=>l=c),l},i);n(a)});T("store",ti);T("data",e=>Ht(e));T("root",e=>xe(e));T("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=le(Hi(e))),e._x_refs_proxy));function Hi(e){let t=[];return Y(e,n=>{n._x_refs&&t.push(n._x_refs)}),t}var $e={};function Dn(e){return $e[e]||($e[e]=0),++$e[e]}function qi(e,t){return Y(e,n=>{if(n._x_ids&&n._x_ids[t])return!0})}function Ki(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=Dn(t))}T("id",(e,{cleanup:t})=>(n,r=null)=>{let i=`${n}${r?`-${r}`:""}`;return Wi(e,i,t,()=>{let s=qi(e,n),o=s?s._x_ids[n]:Dn(n);return r?`${n}-${o}-${r}`:`${n}-${o}`})});Ee((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function Wi(e,t,n,r){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let i=r();return e._x_id[t]=i,n(()=>{delete e._x_id[t]}),i}T("el",e=>e);Un("Focus","focus","focus");Un("Persist","persist","persist");function Un(e,t,n){T(t,r=>A(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}w("modelable",(e,{expression:t},{effect:n,evaluateLater:r,cleanup:i})=>{let s=r(t),o=()=>{let u;return s(f=>u=f),u},a=r(`${t} = __placeholder`),l=u=>a(()=>{},{scope:{__placeholder:u}}),c=o();l(c),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let u=e._x_model.get,f=e._x_model.set,h=En({get(){return u()},set(y){f(y)}},{get(){return o()},set(y){l(y)}});i(h)})});w("teleport",(e,{modifiers:t,expression:n},{cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&A("x-teleport can only be used on a <template> tag",e);let i=Tt(n),s=e.content.cloneNode(!0).firstElementChild;e._x_teleport=s,s._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),s.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(a=>{s.addEventListener(a,l=>{l.stopPropagation(),e.dispatchEvent(new l.constructor(l.type,l))})}),ae(s,{},e);let o=(a,l,c)=>{c.includes("prepend")?l.parentNode.insertBefore(a,l):c.includes("append")?l.parentNode.insertBefore(a,l.nextSibling):l.appendChild(a)};_(()=>{o(s,i,t),j(()=>{k(s)})()}),e._x_teleportPutBack=()=>{let a=Tt(n);_(()=>{o(e._x_teleport,a,t)})},r(()=>_(()=>{s.remove(),Z(s)}))});var Ji=document.createElement("div");function Tt(e){let t=j(()=>document.querySelector(e),()=>Ji)();return t||A(`Cannot find x-teleport element for selector: "${e}"`),t}var zn=()=>{};zn.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};w("ignore",zn);w("effect",j((e,{expression:t},{effect:n})=>{n(b(e,t))}));function Ge(e,t,n,r){let i=e,s=l=>r(l),o={},a=(l,c)=>u=>c(l,u);if(n.includes("dot")&&(t=Vi(t)),n.includes("camel")&&(t=Gi(t)),n.includes("passive")&&(o.passive=!0),n.includes("capture")&&(o.capture=!0),n.includes("window")&&(i=window),n.includes("document")&&(i=document),n.includes("debounce")){let l=n[n.indexOf("debounce")+1]||"invalid-wait",c=ve(l.split("ms")[0])?Number(l.split("ms")[0]):250;s=xn(s,c)}if(n.includes("throttle")){let l=n[n.indexOf("throttle")+1]||"invalid-wait",c=ve(l.split("ms")[0])?Number(l.split("ms")[0]):250;s=bn(s,c)}return n.includes("prevent")&&(s=a(s,(l,c)=>{c.preventDefault(),l(c)})),n.includes("stop")&&(s=a(s,(l,c)=>{c.stopPropagation(),l(c)})),n.includes("once")&&(s=a(s,(l,c)=>{l(c),i.removeEventListener(t,s,o)})),(n.includes("away")||n.includes("outside"))&&(i=document,s=a(s,(l,c)=>{e.contains(c.target)||c.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&l(c))})),n.includes("self")&&(s=a(s,(l,c)=>{c.target===e&&l(c)})),(Yi(t)||Hn(t))&&(s=a(s,(l,c)=>{Zi(c,n)||l(c)})),i.addEventListener(t,s,o),()=>{i.removeEventListener(t,s,o)}}function Vi(e){return e.replace(/-/g,".")}function Gi(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function ve(e){return!Array.isArray(e)&&!isNaN(e)}function Xi(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function Yi(e){return["keydown","keyup"].includes(e)}function Hn(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function Zi(e,t){let n=t.filter(s=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(s));if(n.includes("debounce")){let s=n.indexOf("debounce");n.splice(s,ve((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.includes("throttle")){let s=n.indexOf("throttle");n.splice(s,ve((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0||n.length===1&&Ot(e.key).includes(n[0]))return!1;const i=["ctrl","shift","alt","meta","cmd","super"].filter(s=>n.includes(s));return n=n.filter(s=>!i.includes(s)),!(i.length>0&&i.filter(o=>((o==="cmd"||o==="super")&&(o="meta"),e[`${o}Key`])).length===i.length&&(Hn(e.type)||Ot(e.key).includes(n[0])))}function Ot(e){if(!e)return[];e=Xi(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(n=>{if(t[n]===e)return n}).filter(n=>n)}w("model",(e,{modifiers:t,expression:n},{effect:r,cleanup:i})=>{let s=e;t.includes("parent")&&(s=e.parentNode);let o=b(s,n),a;typeof n=="string"?a=b(s,`${n} = __placeholder`):typeof n=="function"&&typeof n()=="string"?a=b(s,`${n()} = __placeholder`):a=()=>{};let l=()=>{let h;return o(y=>h=y),$t(h)?h.get():h},c=h=>{let y;o(M=>y=M),$t(y)?y.set(h):a(()=>{},{scope:{__placeholder:h}})};typeof n=="string"&&e.type==="radio"&&_(()=>{e.hasAttribute("name")||e.setAttribute("name",n)});var u=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let f=L?()=>{}:Ge(e,u,t,h=>{c(ke(e,t,h,l()))});if(t.includes("fill")&&([void 0,null,""].includes(l())||ct(e)&&Array.isArray(l())||e.tagName.toLowerCase()==="select"&&e.multiple)&&c(ke(e,t,{target:e},l())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=f,i(()=>e._x_removeModelListeners.default()),e.form){let h=Ge(e.form,"reset",[],y=>{at(()=>e._x_model&&e._x_model.set(ke(e,t,{target:e},l())))});i(()=>h())}e._x_model={get(){return l()},set(h){c(h)}},e._x_forceModelUpdate=h=>{h===void 0&&typeof n=="string"&&n.match(/\./)&&(h=""),window.fromModel=!0,_(()=>mn(e,"value",h)),delete window.fromModel},r(()=>{let h=l();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(h)})});function ke(e,t,n,r){return _(()=>{if(n instanceof CustomEvent&&n.detail!==void 0)return n.detail!==null&&n.detail!==void 0?n.detail:n.target.value;if(ct(e))if(Array.isArray(r)){let i=null;return t.includes("number")?i=Pe(n.target.value):t.includes("boolean")?i=me(n.target.value):i=n.target.value,n.target.checked?r.includes(i)?r:r.concat([i]):r.filter(s=>!Qi(s,i))}else return n.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(n.target.selectedOptions).map(i=>{let s=i.value||i.text;return Pe(s)}):t.includes("boolean")?Array.from(n.target.selectedOptions).map(i=>{let s=i.value||i.text;return me(s)}):Array.from(n.target.selectedOptions).map(i=>i.value||i.text);{let i;return vn(e)?n.target.checked?i=n.target.value:i=r:i=n.target.value,t.includes("number")?Pe(i):t.includes("boolean")?me(i):t.includes("trim")?i.trim():i}}})}function Pe(e){let t=e?parseFloat(e):null;return es(t)?t:e}function Qi(e,t){return e==t}function es(e){return!Array.isArray(e)&&!isNaN(e)}function $t(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}w("cloak",e=>queueMicrotask(()=>_(()=>e.removeAttribute(X("cloak")))));un(()=>`[${X("init")}]`);w("init",j((e,{expression:t},{evaluate:n})=>typeof t=="string"?!!t.trim()&&n(t,{},!1):n(t,{},!1)));w("text",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(s=>{_(()=>{e.textContent=s})})})});w("html",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(s=>{_(()=>{e.innerHTML=s,e._x_ignoreSelf=!0,k(e),delete e._x_ignoreSelf})})})});it(Qt(":",en(X("bind:"))));var qn=(e,{value:t,modifiers:n,expression:r,original:i},{effect:s,cleanup:o})=>{if(!t){let l={};ri(l),b(e,r)(u=>{An(e,u,i)},{scope:l});return}if(t==="key")return ts(e,r);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let a=b(e,r);s(()=>a(l=>{l===void 0&&typeof r=="string"&&r.match(/\./)&&(l=""),_(()=>mn(e,t,l,n))})),o(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};qn.inline=(e,{value:t,modifiers:n,expression:r})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:r,extract:!1})};w("bind",qn);function ts(e,t){e._x_keyExpression=t}cn(()=>`[${X("data")}]`);w("data",(e,{expression:t},{cleanup:n})=>{if(ns(e))return;t=t===""?"{}":t;let r={};Re(r,e);let i={};si(i,r);let s=B(e,t,{scope:i});(s===void 0||s===!0)&&(s={}),Re(s,e);let o=V(s);qt(o);let a=ae(e,o);o.init&&B(e,o.init),n(()=>{o.destroy&&B(e,o.destroy),a()})});Ee((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function ns(e){return L?Ke?!0:e.hasAttribute("data-has-alpine-state"):!1}w("show",(e,{modifiers:t,expression:n},{effect:r})=>{let i=b(e,n);e._x_doHide||(e._x_doHide=()=>{_(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{_(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let s=()=>{e._x_doHide(),e._x_isShown=!1},o=()=>{e._x_doShow(),e._x_isShown=!0},a=()=>setTimeout(o),l=He(f=>f?o():s(),f=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,f,o,s):f?a():s()}),c,u=!0;r(()=>i(f=>{!u&&f===c||(t.includes("immediate")&&(f?a():s()),l(f),c=f,u=!1)}))});w("for",(e,{expression:t},{effect:n,cleanup:r})=>{let i=is(t),s=b(e,i.items),o=b(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>rs(e,i,s,o)),r(()=>{Object.values(e._x_lookup).forEach(a=>_(()=>{Z(a),a.remove()})),delete e._x_prevKeys,delete e._x_lookup})});function rs(e,t,n,r){let i=o=>typeof o=="object"&&!Array.isArray(o),s=e;n(o=>{ss(o)&&o>=0&&(o=Array.from(Array(o).keys(),d=>d+1)),o===void 0&&(o=[]);let a=e._x_lookup,l=e._x_prevKeys,c=[],u=[];if(i(o))o=Object.entries(o).map(([d,p])=>{let m=kt(t,p,d,o);r(v=>{u.includes(v)&&A("Duplicate key on x-for",e),u.push(v)},{scope:{index:d,...m}}),c.push(m)});else for(let d=0;d<o.length;d++){let p=kt(t,o[d],d,o);r(m=>{u.includes(m)&&A("Duplicate key on x-for",e),u.push(m)},{scope:{index:d,...p}}),c.push(p)}let f=[],h=[],y=[],M=[];for(let d=0;d<l.length;d++){let p=l[d];u.indexOf(p)===-1&&y.push(p)}l=l.filter(d=>!y.includes(d));let $="template";for(let d=0;d<u.length;d++){let p=u[d],m=l.indexOf(p);if(m===-1)l.splice(d,0,p),f.push([$,d]);else if(m!==d){let v=l.splice(d,1)[0],S=l.splice(m-1,1)[0];l.splice(d,0,S),l.splice(m,0,v),h.push([v,S])}else M.push(p);$=p}for(let d=0;d<y.length;d++){let p=y[d];p in a&&(_(()=>{Z(a[p]),a[p].remove()}),delete a[p])}for(let d=0;d<h.length;d++){let[p,m]=h[d],v=a[p],S=a[m],q=document.createElement("div");_(()=>{S||A('x-for ":key" is undefined or invalid',s,m,a),S.after(q),v.after(S),S._x_currentIfEl&&S.after(S._x_currentIfEl),q.before(v),v._x_currentIfEl&&v.after(v._x_currentIfEl),q.remove()}),S._x_refreshXForScope(c[u.indexOf(m)])}for(let d=0;d<f.length;d++){let[p,m]=f[d],v=p==="template"?s:a[p];v._x_currentIfEl&&(v=v._x_currentIfEl);let S=c[m],q=u[m],Q=document.importNode(s.content,!0).firstElementChild,_t=V(S);ae(Q,_t,s),Q._x_refreshXForScope=Jn=>{Object.entries(Jn).forEach(([Vn,Gn])=>{_t[Vn]=Gn})},_(()=>{v.after(Q),j(()=>k(Q))()}),typeof q=="object"&&A("x-for key cannot be an object, it must be a string or an integer",s),a[q]=Q}for(let d=0;d<M.length;d++)a[M[d]]._x_refreshXForScope(c[u.indexOf(M[d])]);s._x_prevKeys=u})}function is(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,n=/^\s*\(|\)\s*$/g,r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,i=e.match(r);if(!i)return;let s={};s.items=i[2].trim();let o=i[1].replace(n,"").trim(),a=o.match(t);return a?(s.item=o.replace(t,"").trim(),s.index=a[1].trim(),a[2]&&(s.collection=a[2].trim())):s.item=o,s}function kt(e,t,n,r){let i={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(o=>o.trim()).forEach((o,a)=>{i[o]=t[a]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(o=>o.trim()).forEach(o=>{i[o]=t[o]}):i[e.item]=t,e.index&&(i[e.index]=n),e.collection&&(i[e.collection]=r),i}function ss(e){return!Array.isArray(e)&&!isNaN(e)}function Kn(){}Kn.inline=(e,{expression:t},{cleanup:n})=>{let r=xe(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])};w("ref",Kn);w("if",(e,{expression:t},{effect:n,cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&A("x-if can only be used on a <template> tag",e);let i=b(e,t),s=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let a=e.content.cloneNode(!0).firstElementChild;return ae(a,{},e),_(()=>{e.after(a),j(()=>k(a))()}),e._x_currentIfEl=a,e._x_undoIf=()=>{_(()=>{Z(a),a.remove()}),delete e._x_currentIfEl},a},o=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};n(()=>i(a=>{a?s():o()})),r(()=>e._x_undoIf&&e._x_undoIf())});w("id",(e,{expression:t},{evaluate:n})=>{n(t).forEach(i=>Ki(e,i))});Ee((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});it(Qt("@",en(X("on:"))));w("on",j((e,{value:t,modifiers:n,expression:r},{cleanup:i})=>{let s=r?b(e,r):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let o=Ge(e,t,n,a=>{s(()=>{},{scope:{$event:a},params:[a]})});i(()=>o())}));Ce("Collapse","collapse","collapse");Ce("Intersect","intersect","intersect");Ce("Focus","trap","focus");Ce("Mask","mask","mask");function Ce(e,t,n){w(t,r=>A(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}ce.setEvaluator(Gt);ce.setReactivityEngine({reactive:mt,effect:gi,release:mi,raw:g});var os=ce,E=os;class as{constructor(t="/api"){this.baseURL=t,this.defaultHeaders={"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"}}async request(t,n={}){const r={headers:{...this.defaultHeaders,...n.headers},...n},i=t.startsWith("http")?t:`${this.baseURL}${t}`;try{const s=await fetch(i,r);if(!s.ok)throw new K(`HTTP ${s.status}: ${s.statusText}`,s.status,s);const o=s.headers.get("content-type");return o&&o.includes("application/json")?await s.json():await s.text()}catch(s){throw s instanceof K?s:new K(`Network error: ${s.message}`,0,null,s)}}async get(t,n={}){const i=new URLSearchParams(n).toString(),s=i?`${t}?${i}`:t;return this.request(s,{method:"GET"})}async post(t,n={}){return this.request(t,{method:"POST",body:JSON.stringify(n)})}async put(t,n={}){return this.request(t,{method:"PUT",body:JSON.stringify(n)})}async patch(t,n={}){return this.request(t,{method:"PATCH",body:JSON.stringify(n)})}async delete(t){return this.request(t,{method:"DELETE"})}async postFormData(t,n){const r={...this.defaultHeaders};return delete r["Content-Type"],this.request(t,{method:"POST",headers:r,body:n})}async submitForm(t,n="POST"){const r=new FormData(t),i=t.getAttribute("data-action")||t.action,s={};for(let[o,a]of r.entries())s[o]?Array.isArray(s[o])?s[o].push(a):s[o]=[s[o],a]:s[o]=a;switch(n.toUpperCase()){case"GET":return this.get(i,s);case"POST":return this.post(i,s);case"PUT":return this.put(i,s);case"PATCH":return this.patch(i,s);case"DELETE":return this.delete(i);default:throw new K(`Unsupported HTTP method: ${n}`)}}setDefaultHeaders(t){this.defaultHeaders={...this.defaultHeaders,...t}}setBaseURL(t){this.baseURL=t}}class K extends Error{constructor(t,n=0,r=null,i=null){super(t),this.name="ApiError",this.status=n,this.response=r,this.originalError=i}async getDetails(){if(this.response)try{const t=this.response.headers.get("content-type");return t&&t.includes("application/json")?await this.response.json():await this.response.text()}catch{return this.message}return this.message}}const x=new as;window.submitForm=async function(e){var i,s,o;const t=e.target,n=t.querySelector('button[type="submit"]'),r=n==null?void 0:n.textContent;try{n&&(n.disabled=!0,n.textContent="Submitting...");const a=t.getAttribute("data-method")||"POST",l=await x.submitForm(t,a);console.log("Form submitted successfully:",l),Pt("Data saved successfully","success");const c=(o=(s=(i=t.closest("[x-data]"))==null?void 0:i.querySelector("[x-on\\:open-modal]"))==null?void 0:s.getAttribute("x-on:open-modal"))==null?void 0:o.replace(".window","").replace("open-modal-","");c&&window.dispatchEvent(new CustomEvent(`close-modal-${c}`)),t.reset(),window.dispatchEvent(new CustomEvent("form-submitted",{detail:{form:t,response:l,method:a}}))}catch(a){console.error("Form submission error:",a);let l="An error occurred while submitting the form";if(a instanceof K)try{const c=await a.getDetails();typeof c=="object"&&c.message?l=c.message:typeof c=="string"&&(l=c)}catch{l=a.message}else l=a.message;Pt(l,"error")}finally{n&&(n.disabled=!1,n.textContent=r)}};function Pt(e,t="info"){console.log(`${t.toUpperCase()}: ${e}`),t==="error"?alert(`Error: ${e}`):t==="success"&&console.log(`✅ ${e}`)}window.api=x;window.ApiError=K;E.data("dashboardStats",()=>({loading:!1,stats:{users:{total:0,active:0,inactive:0},plans:{total:0,active:0,inactive:0},enbs:{total:0,online:0,offline:0}},async loadStats(){this.loading=!0;try{const e=await x.get("/dashboard/stats");this.stats=e.data}catch(e){console.error("Failed to load dashboard stats:",e),window.utils.showToast("error","Failed to load dashboard statistics")}finally{this.loading=!1}}}));E.data("usersManager",()=>({loading:!1,saving:!1,users:[],plans:[],filteredUsers:[],showModal:!1,modalMode:"create",currentUser:{},filters:{search:"",status:"",plan:""},async loadUsers(){this.loading=!0;try{const{data:e}=await x.get("/users");this.users=users}catch(e){console.error("Error loading users:",e),showNotification("Error loading users","error")}finally{this.loading=!1}},applyFilters(){let e=[...this.users];if(this.filters.search){const t=this.filters.search.toLowerCase();e=e.filter(n=>(n.name||"").toLowerCase().includes(t)||(n.email||"").toLowerCase().includes(t))}this.filters.status&&(e=e.filter(t=>t.status===this.filters.status)),this.filters.plan&&(e=e.filter(t=>t.plan_id===this.filters.plan)),this.filteredUsers=e},resetFilters(){this.filters={search:"",status:"",plan:""},this.applyFilters()},openCreateModal(){this.modalMode="create",this.currentUser={status:"active"},this.showModal=!0},editUser(e){this.modalMode="edit",this.currentUser={...e},this.showModal=!0},closeModal(){this.showModal=!1,this.currentUser={}},async saveUser(){this.saving=!0;try{let e;this.modalMode==="create"?e=await x.post("/users",this.currentUser):e=await x.put(`/users/${this.currentUser._id}`,this.currentUser),e.success?(window.utils.showToast("success",this.modalMode==="create"?"User created successfully":"User updated successfully"),this.closeModal(),await this.loadUsers()):window.utils.showToast("error",e.message||"Operation failed")}catch(e){console.error("Failed to save user:",e),window.utils.showToast("error","Failed to save user")}finally{this.saving=!1}},async deleteUser(e){if(confirm("Are you sure you want to delete this user?"))try{const t=await x.delete(`/users/${e}`);t.success?(window.utils.showToast("success","User deleted successfully"),await this.loadUsers()):window.utils.showToast("error",t.message||"Delete failed")}catch(t){console.error("Failed to delete user:",t),window.utils.showToast("error","Failed to delete user")}},formatDate(e){return window.utils.formatDate(e)}}));E.data("plansManager",()=>({loading:!1,saving:!1,plans:[],showModal:!1,modalMode:"create",currentPlan:{},async loadPlans(){this.loading=!0;try{const e=await x.get("/plans");this.plans=e.data||e}catch(e){console.error("Failed to load plans:",e),window.utils.showToast("error","Failed to load plans")}finally{this.loading=!1}},openCreateModal(){this.modalMode="create",this.currentPlan={status:"active",support_level:"basic",price:0},this.showModal=!0},editPlan(e){this.modalMode="edit",this.currentPlan={...e},this.showModal=!0},closeModal(){this.showModal=!1,this.currentPlan={}},async savePlan(){this.saving=!0;try{let e;this.modalMode==="create"?e=await x.post("/plans",this.currentPlan):e=await x.put(`/plans/${this.currentPlan._id}`,this.currentPlan),e.success?(window.utils.showToast("success",this.modalMode==="create"?"Plan created successfully":"Plan updated successfully"),this.closeModal(),await this.loadPlans()):window.utils.showToast("error",e.message||"Operation failed")}catch(e){console.error("Failed to save plan:",e),window.utils.showToast("error","Failed to save plan")}finally{this.saving=!1}},async deletePlan(e){if(confirm("Are you sure you want to delete this plan?"))try{const t=await x.delete(`/plans/${e}`);t.success?(window.utils.showToast("success","Plan deleted successfully"),await this.loadPlans()):window.utils.showToast("error",t.message||"Delete failed")}catch(t){console.error("Failed to delete plan:",t),window.utils.showToast("error","Failed to delete plan")}}}));E.data("enbsManager",()=>({loading:!1,saving:!1,enbs:[],filteredEnbs:[],showModal:!1,modalMode:"create",currentEnb:{},filters:{search:"",status:""},async loadEnbs(){this.loading=!0;try{const e=await x.get("/enbs");this.enbs=e.data||e,this.applyFilters()}catch(e){console.error("Failed to load eNBs:",e),window.utils.showToast("error","Failed to load eNBs")}finally{this.loading=!1}},applyFilters(){let e=[...this.enbs];if(this.filters.search){const t=this.filters.search.toLowerCase();e=e.filter(n=>(n.name||"").toLowerCase().includes(t)||(n.cell_id||"").toString().includes(t)||(n.location||"").toLowerCase().includes(t))}this.filters.status&&(e=e.filter(t=>t.status===this.filters.status)),this.filteredEnbs=e},resetFilters(){this.filters={search:"",status:""},this.applyFilters()},openCreateModal(){this.modalMode="create",this.currentEnb={status:"active"},this.showModal=!0},editEnb(e){this.modalMode="edit",this.currentEnb={...e},this.showModal=!0},closeModal(){this.showModal=!1,this.currentEnb={}},async saveEnb(){this.saving=!0;try{let e;this.modalMode==="create"?e=await x.post("/enbs",this.currentEnb):e=await x.put(`/enbs/${this.currentEnb._id}`,this.currentEnb),e.success?(window.utils.showToast("success",this.modalMode==="create"?"eNB created successfully":"eNB updated successfully"),this.closeModal(),await this.loadEnbs()):window.utils.showToast("error",e.message||"Operation failed")}catch(e){console.error("Failed to save eNB:",e),window.utils.showToast("error","Failed to save eNB")}finally{this.saving=!1}},async deleteEnb(e){if(confirm("Are you sure you want to delete this eNB?"))try{const t=await x.delete(`/enbs/${e}`);t.success?(window.utils.showToast("success","eNB deleted successfully"),await this.loadEnbs()):window.utils.showToast("error",t.message||"Delete failed")}catch(t){console.error("Failed to delete eNB:",t),window.utils.showToast("error","Failed to delete eNB")}},formatDate(e){return window.utils.formatDate(e)}}));E.data("notificationManager",()=>({get notifications(){return E.store("notifications").items},removeNotification(e){E.store("notifications").remove(e)},getTypeClass(e){return{success:"toast-success",error:"toast-error",warning:"toast-warning",info:"toast-info"}[e]||"toast-info"},getIcon(e){const t={success:`<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>`,error:`<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>`,warning:`<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>`,info:`<svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>`};return t[e]||t.info}}));document.addEventListener("alpine:init",()=>{let e=document.getElementById("toast-container");e||(e=document.createElement("div"),e.id="toast-container",e.className="fixed top-4 right-4 z-50 space-y-2",e.setAttribute("x-data","notificationManager"),e.innerHTML=`
            <template x-for="notification in notifications" :key="notification.id">
                <div class="toast animate-slide-in" 
                     :class="getTypeClass(notification.type)"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95">
                    <div class="flex-1 w-0 p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0" x-html="getIcon(notification.type)"></div>
                            <div class="ml-3 w-0 flex-1 pt-0.5">
                                <p class="text-sm font-medium text-gray-900" x-text="notification.message"></p>
                            </div>
                            <div class="ml-4 flex-shrink-0 flex">
                                <button @click="removeNotification(notification.id)"
                                        class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <span class="sr-only">Close</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        `,document.body.appendChild(e))});function ls(e){window.dispatchEvent(new CustomEvent(`open-modal-${e}`))}function Wn(e){window.dispatchEvent(new CustomEvent(`close-modal-${e}`))}function cs(e){window.dispatchEvent(new CustomEvent(`toggle-modal-${e}`))}function us({title:e="Confirm Action",message:t="Are you sure?",confirmText:n="Confirm",cancelText:r="Cancel",confirmClass:i="bg-red-600 hover:bg-red-700 text-white",onConfirm:s=null,onCancel:o=null}){const a="dynamicConfirmModal",l=document.getElementById(`${a}Container`);l&&l.remove();const c=`
        <div id="${a}Container" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                
                <!-- This element is to trick the browser into centering the modal contents. -->
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="${a}Title">
                                    ${e}
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500" id="${a}Message">
                                        ${t}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button" 
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm ${i}" 
                                id="${a}Confirm">
                            ${n}
                        </button>
                        <button type="button" 
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" 
                                id="${a}Cancel">
                            ${r}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;document.body.insertAdjacentHTML("beforeend",c);const u=document.getElementById(`${a}Container`),f=document.getElementById(`${a}Confirm`),h=document.getElementById(`${a}Cancel`),y=u.querySelector(".fixed.inset-0.bg-gray-500");u.style.display="block",setTimeout(()=>{u.classList.add("opacity-100"),u.querySelector(".inline-block").classList.add("scale-100")},10);const M=()=>{s&&typeof s=="function"&&s(),d()},$=()=>{o&&typeof o=="function"&&o(),d()},d=()=>{u.style.display="none",setTimeout(()=>{u.remove()},300)};f.addEventListener("click",M),h.addEventListener("click",$),y.addEventListener("click",$);const p=m=>{m.key==="Escape"&&($(),document.removeEventListener("keydown",p))};return document.addEventListener("keydown",p),{confirm:M,cancel:$,hide:d}}function ds({title:e="Information",message:t="",buttonText:n="Close",onClose:r=null}){const i="dynamicInfoModal",s=document.getElementById(`${i}Container`);s&&s.remove();const o=`
        <div id="${i}Container" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                
                <!-- This element is to trick the browser into centering the modal contents. -->
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    ${e}
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">
                                        ${t}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button" 
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm" 
                                id="${i}Close">
                            ${n}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;document.body.insertAdjacentHTML("beforeend",o);const a=document.getElementById(`${i}Container`),l=document.getElementById(`${i}Close`),c=a.querySelector(".fixed.inset-0.bg-gray-500");a.style.display="block",setTimeout(()=>{a.classList.add("opacity-100"),a.querySelector(".inline-block").classList.add("scale-100")},10);const u=()=>{r&&typeof r=="function"&&r(),f()},f=()=>{a.style.display="none",setTimeout(()=>{a.remove()},300)};l.addEventListener("click",u),c.addEventListener("click",u);const h=y=>{y.key==="Escape"&&(u(),document.removeEventListener("keydown",h))};return document.addEventListener("keydown",h),{close:u,hide:f}}function fs(){document.querySelectorAll('[x-data*="modal"]').forEach(n=>{if(n.style.display!=="none"){const r=n.id;r&&Wn(r)}}),document.querySelectorAll('[id$="Container"]').forEach(n=>{n.id.includes("Modal")&&n.remove()})}window.openModal=ls;window.closeModal=Wn;window.toggleModal=cs;window.showConfirmModal=us;window.showInfoModal=ds;window.closeAllModals=fs;window.Alpine=E;E.store("auth",{user:null,token:localStorage.getItem("token")||null,async login(e,t){try{const n=await fetch("/api/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),r=await n.json();return n.ok?(this.token=r.token,this.user=r.user,localStorage.setItem("token",r.token),{success:!0}):{success:!1,message:r.message}}catch{return{success:!1,message:"Network error"}}},logout(){this.token=null,this.user=null,localStorage.removeItem("token"),window.location.href="/login"},isAuthenticated(){return!!this.token}});E.store("notifications",{items:[],add(e,t,n=5e3){const r=Date.now();this.items.push({id:r,type:e,message:t}),n>0&&setTimeout(()=>this.remove(r),n)},remove(e){this.items=this.items.filter(t=>t.id!==e)},clear(){this.items=[]}});window.utils={formatDate(e){return e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"N/A"},formatDateTime(e){return e?new Date(e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A"},formatCurrency(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e||0)},debounce(e,t){let n;return function(...i){const s=()=>{clearTimeout(n),e(...i)};clearTimeout(n),n=setTimeout(s,t)}},showToast(e,t){E.store("notifications").add(e,t)}};const Lt=document.querySelector('meta[name="csrf-token"]');if(Lt){window.csrfToken=Lt.getAttribute("content");const e=window.fetch;window.fetch=function(t,n={}){n.headers={"X-CSRF-TOKEN":window.csrfToken,"Content-Type":"application/json",Accept:"application/json",...n.headers};const r=E.store("auth").token;return r&&(n.headers.Authorization=`Bearer ${r}`),e(t,n)}}window.showNotification=function(e,t="info"){console.log(`${t.toUpperCase()}: ${e}`),t==="error"?alert(`Error: ${e}`):t==="success"&&console.log(`✅ ${e}`)};E.start();console.log("RapidSGS Pro application loaded successfully");
