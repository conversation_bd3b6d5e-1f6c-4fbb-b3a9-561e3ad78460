@extends('layout')

@section('title', 'System Overview')

@section('content')
<div x-data="dashboardComponent()" x-cloak class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">System Overview</h1>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

        <!-- Download Throughput Card -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-lg p-6 relative overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-green-500/10"></div>

            <!-- Content -->
            <div class="relative z-10 flex flex-col items-center h-full min-h-[200px]">

                <!-- Title -->
                <div class="text-center mb-4">
                    <h3 class="text-2xl font-medium text-gray-300 mb-1">Download Throughput</h3>
                </div>

                <!-- Circular Progress Indicator -->
                <div class="relative w-48 h-48 mb-4">
                    <!-- Background circle -->
                    <svg class="w-48 h-48 transform rotate-90" viewBox="0 0 120 120">
                        <circle cx="60" cy="60" r="50"
                                fill="none"
                                stroke="rgba(75, 85, 99, 0.3)"
                                stroke-width="10"/>

                        <!-- Progress arc with gradient -->
                        <defs>
                            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#06B6D4;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <circle cx="60" cy="60" r="50"
                                fill="none"
                                stroke="url(#gradient)"
                                stroke-width="10"
                                stroke-linecap="round"
                                stroke-dasharray="0 314"
                                :stroke-dasharray="`${(stats.download || 0) / 1000 * 314} 314`"
                                class="transition-all duration-1000 ease-out"/>
                    </svg>

                    <!-- Center content -->
                    <div class="absolute inset-0 flex pt-8 flex-col items-center justify-center">
                        <div class="text-xl font-bold text-green-400" x-text="`${stats.download || 0} Mbps`"></div>
                        <h3 class="text-sm text-gray-300">Download</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Throughput Card -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-lg p-6 relative overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-green-500/10"></div>

            <!-- Content -->
            <div class="relative z-10 flex flex-col items-center h-full min-h-[200px]">

                <!-- Title -->
                <div class="text-center mb-4">
                    <h3 class="text-2xl font-medium text-gray-300 mb-1">Upload Throughput</h3>
                </div>

                <!-- Circular Progress Indicator -->
                <div class="relative w-48 h-48 mb-4">
                    <!-- Background circle -->
                    <svg class="w-48 h-48 transform rotate-90" viewBox="0 0 120 120">
                        <circle cx="60" cy="60" r="50"
                                fill="none"
                                stroke="rgba(75, 85, 99, 0.3)"
                                stroke-width="10"/>

                        <!-- Progress arc with gradient -->
                        <defs>
                            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#06B6D4;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#10B981;stop-opacity:1" />
                            </linearGradient>
                        </defs>

                        <circle cx="60" cy="60" r="50"
                                fill="none"
                                stroke="url(#gradient)"
                                stroke-width="10"
                                stroke-linecap="round"
                                stroke-dasharray="0 314"
                                :stroke-dasharray="`${(stats.upload || 0) / 1000 * 314} 314`"
                                class="transition-all duration-1000 ease-out"/>
                    </svg>

                    <!-- Center content -->
                    <div class="absolute inset-0 flex pt-8 flex-col items-center justify-center">
                        <div class="text-xl font-bold text-green-400" x-text="`${stats.upload || 0} Mbps`"></div>
                        <h3 class="text-sm text-gray-300">Upload</h3>
                    </div>
                </div>
            </div>
        </div>


        <!-- System Uptime Card -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-lg p-6 relative overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-green-500/10"></div>

            <!-- Content -->
            <div class="relative z-10 flex flex-col h-full min-h-[200px]">

                <!-- Title -->
                <div class="mb-4">
                    <h3 class="text-2xl font-medium text-gray-300 mb-1">System Uptime</h3>
                </div>

                <!-- Content -->
                <div class="flex-1" x-show="!loading" x-transition>
                    <dl>
                        <dt class="text-4xl font-bold text-green-400 truncate mb-1" x-text="calculateUptime()"></dt>
                        <dd class="text-sm text-gray-300">Last restart: <span x-text="stats.last_restart"></span></dd>
                    </dl>
                </div>

            </div>
        </div>

        <!-- Active Users Card -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-lg p-6 relative overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-green-500/10"></div>

            <!-- Content -->
            <div class="relative z-10 flex flex-col h-full min-h-[200px]">

                <!-- Title -->
                <div class="mb-4">
                    <h3 class="text-2xl font-medium text-gray-300 mb-1">Active Users</h3>
                </div>

                <!-- Content -->
                <div class="flex-1" x-show="!loading" x-transition>
                    <dl>
                        <dt class="text-4xl font-bold text-green-400 truncate mb-1">
                            <span x-text="stats?.users?.active || 0"></span>/<span x-text="stats?.users?.total || 0"></span>
                        </dt>
                        <dd class="text-sm text-gray-300" x-text="calculateUserPercentage()"></dd>
                    </dl>
                </div>

            </div>
        </div>


        <!-- eNB Status Card -->
        <div class="col-span-2 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-lg p-6 relative overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-green-500/10"></div>

            <!-- Content -->
            <div class="relative z-10 flex flex-col h-full min-h-[200px]">

                <!-- Title -->
                <div class="mb-4">
                    <h3 class="text-2xl font-medium text-gray-300 mb-1">eNB Status</h3>
                </div>

                <!-- Content -->
                <div class="flex-1" x-show="!loading" x-transition>
                    <dl class="flex justify-between items-baseline">
                        <dt class="text-md text-gray-300">Connected eNBs</dt>
                        <dd class="text-2xl font-bold text-green-400 truncate mb-1" x-text="calculatedEnbs()"></dd>
                    </dl>
                </div>

            </div>
        </div>

        <!-- Network Latency Card -->
        <div class="col-span-2 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-lg p-6 relative overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-green-500/10"></div>

            <!-- Content -->
            <div class="relative z-10 flex flex-col h-full min-h-[200px]">

                <!-- Title -->
                <div class="mb-4">
                    <h3 class="text-2xl font-medium text-gray-300">Network Latency</h3>
                </div>

                <!-- Content -->
                <div class="flex-1" x-show="!loading" x-transition>
                    <div class="flex justify-between items-baseline mb-3">
                        <div class="text-md text-gray-300">Google</div>
                        <div class="text-lg font-bold text-green-400 truncate bg-black/20 px-3 py-1"><span x-text="stats.latency.google"></span>ms</div>
                    </div>
                    <div class="flex justify-between items-baseline mb-3">
                        <div class="text-md text-gray-300">Cloudflare</div>
                        <div class="text-lg font-bold text-green-400 truncate bg-black/20 px-3 py-1"><span x-text="stats.latency.cloudflare"></span>ms</div>
                    </div>
                    <div class="flex justify-between items-baseline">
                        <div class="text-md text-gray-300">Netflix</div>
                        <div class="text-lg font-bold text-green-400 truncate bg-black/20 px-3 py-1"><span x-text="stats.latency.netflix"></span>ms</div>
                    </div>
                </div>

            </div>
        </div>

    </div>

    <!-- Modal Examples Section -->
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Примеры модальных окон</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button @click="console.log('Opening simple modal'); $dispatch('open-modal-simpleModal')"
                    class="p-4 bg-blue-50 hover:bg-blue-100 rounded-lg text-center transition-colors">
                <div class="text-blue-600 font-medium">Простое модальное окно</div>
                <div class="text-sm text-gray-500 mt-1">Базовая модалка</div>
            </button>

            <button @click="console.log('Opening confirmation modal'); $dispatch('open-modal-confirmationModal')"
                    class="p-4 bg-red-50 hover:bg-red-100 rounded-lg text-center transition-colors">
                <div class="text-red-600 font-medium">Подтверждение</div>
                <div class="text-sm text-gray-500 mt-1">Модалка подтверждения</div>
            </button>

            <button @click="console.log('Opening form modal'); $dispatch('open-modal-formExampleModal')"
                    class="p-4 bg-green-50 hover:bg-green-100 rounded-lg text-center transition-colors">
                <div class="text-green-600 font-medium">Форма</div>
                <div class="text-sm text-gray-500 mt-1">Модалка с формой</div>
            </button>

            <button @click="showDynamicConfirm()"
                    class="p-4 bg-purple-50 hover:bg-purple-100 rounded-lg text-center transition-colors">
                <div class="text-purple-600 font-medium">Динамическая</div>
                <div class="text-sm text-gray-500 mt-1">Создается через JS</div>
            </button>
        </div>
    </div>

    <!-- Simple Modal Example -->
    <x-modal 
        id="simpleModal" 
        title="Простое модальное окно"
        size="md"
        animation="fade">
        
        <div class="text-center py-4">
            <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 mb-4">
                <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Информационное сообщение</h3>
            <p class="text-gray-600">
                Это пример простого модального окна. Здесь может быть любая информация.
            </p>
        </div>

        <x-slot name="footer">
            <div class="flex justify-end">
                <button type="button" 
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2"
                        @click="$dispatch('close-modal-simpleModal')">
                    Понятно
                </button>
            </div>
        </x-slot>
    </x-modal>

    <!-- Confirmation Modal Example -->
    <x-confirm-modal
        id="confirmationModal"
        title="Удаление данных"
        message="Вы действительно хотите удалить все данные? Это действие нельзя отменить."
        confirm-text="Удалить"
        cancel-text="Отмена"
        confirm-class="bg-red-600 hover:bg-red-700 text-white"
        size="md"
        x-on:modal-confirmed-confirmationModal.window="handleConfirmation()" />

    <!-- Form Modal Example -->
    <x-form-modal 
        id="formExampleModal" 
        title="Создать задачу"
        submit-text="Создать"
        size="lg"
        action="#"
        method="POST">
        
        <div class="space-y-4">
            <div>
                <label for="task_title" class="block text-sm font-medium text-gray-700 mb-1">Название задачи</label>
                <input type="text" 
                       id="task_title" 
                       name="title" 
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Введите название задачи">
            </div>
            
            <div>
                <label for="task_description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
                <textarea id="task_description" 
                          name="description" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Введите описание задачи"></textarea>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="task_priority" class="block text-sm font-medium text-gray-700 mb-1">Приоритет</label>
                    <select id="task_priority" 
                            name="priority" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Выберите приоритет</option>
                        <option value="low">Низкий</option>
                        <option value="medium">Средний</option>
                        <option value="high">Высокий</option>
                    </select>
                </div>
                
                <div>
                    <label for="task_due_date" class="block text-sm font-medium text-gray-700 mb-1">Срок выполнения</label>
                    <input type="date" 
                           id="task_due_date" 
                           name="due_date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>
    </x-form-modal>
</div>

<script>
function dashboardComponent() {
    return {
        loading: true,
        stats: {
            users: {
                active: 0,
                inactive: 0,
                total: 0
            },
            uptime: 0,
            last_restart: '',
            plans: 0,
            enbs: {
                total: 0,
                online: 0,
                offline: 0
            },
            tasks: 0,
            download: 0, // Default value in Mbps
            upload: 0
        },

        init() {
            // Ensure loading state is properly set
            this.loading = true;

            // Load stats with a small delay to ensure smooth transition
            this.$nextTick(() => {
                this.loadStats();
            });
        },

        async loadStats() {
            try {
                this.loading = true;

                // Simulate minimum loading time for better UX (optional)
                const [response] = await Promise.all([
                    api.get('/dashboard/stats'),
                    new Promise(resolve => setTimeout(resolve, 500)) // Minimum 500ms loading
                ]);

                this.stats = response.data || this.stats;

                // Update specific stats if they exist
                if (response.data) {
                    this.stats = {
                        ...this.stats,
                        ...response.data
                    };
                }

            } catch (error) {
                console.error('Error loading stats:', error);
                this.showNotification('Failed to load dashboard statistics', 'error');
            } finally {
                this.loading = false;
            }
        },

        calculateUptime() {
            if (this.stats.uptime) {
                const uptime = this.stats.uptime;
                const days = Math.floor(uptime / 1440);
                const hours = Math.floor((uptime % 1440) / 60);
                const minutes = Math.floor(uptime % 60);
                return `${days}d ${hours}h ${minutes}m`;
            } else {
                return '';
            }
        },

        calculateUserPercentage() {
            let percent = 0;
            if (this.stats.users.total) {
                percent = Math.floor((this.stats.users.active / this.stats.users.total) * 100);
            }

            return `${percent}% connected`;
        },

        calculatedEnbs() {
            if (this.stats.enbs.total) {
                return `${this.stats.enbs.online}/${this.stats.enbs.total}`;
            } else {
                return '';
            }
        },

        showUserDetails() {
            showInfoModal({
                title: 'Статистика пользователей',
                content: `
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">${this.stats.users}</div>
                                <div class="text-sm text-gray-600">Всего пользователей</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">${Math.floor(this.stats.users * 0.8)}</div>
                                <div class="text-sm text-gray-600">Активных</div>
                            </div>
                        </div>
                        <p class="text-gray-600">
                            Здесь отображается подробная статистика по пользователям системы.
                        </p>
                    </div>
                `,
                size: 'lg'
            });
        },

        showPlanDetails() {
            showInfoModal({
                title: 'Статистика планов',
                content: `
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">${this.stats.plans}</div>
                                <div class="text-sm text-gray-600">Всего планов</div>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-yellow-600">${Math.floor(this.stats.plans * 0.6)}</div>
                                <div class="text-sm text-gray-600">Активных</div>
                            </div>
                        </div>
                        <p class="text-gray-600">
                            Подробная информация о тарифных планах и их использовании.
                        </p>
                    </div>
                `,
                size: 'lg'
            });
        },

        showEnbDetails() {
            showInfoModal({
                title: 'Статистика eNodeB',
                content: `
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600">${this.stats.enbs}</div>
                                <div class="text-sm text-gray-600">Всего eNodeB</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">${Math.floor(this.stats.enbs * 0.9)}</div>
                                <div class="text-sm text-gray-600">Онлайн</div>
                            </div>
                        </div>
                        <p class="text-gray-600">
                            Состояние базовых станций и их производительность.
                        </p>
                    </div>
                `,
                size: 'lg'
            });
        },

        showTaskDetails() {
            showInfoModal({
                title: 'Системные задачи',
                content: `
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-red-600">${this.stats.tasks}</div>
                                <div class="text-sm text-gray-600">Всего задач</div>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-yellow-600">${Math.floor(this.stats.tasks * 0.3)}</div>
                                <div class="text-sm text-gray-600">В обработке</div>
                            </div>
                        </div>
                        <p class="text-gray-600">
                            Информация о фоновых задачах и их выполнении.
                        </p>
                    </div>
                `,
                size: 'lg'
            });
        },

        showDynamicConfirm() {
            showConfirmModal({
                title: 'Динамическое подтверждение',
                message: 'Это модальное окно создано динамически через JavaScript. Хотите продолжить?',
                confirmText: 'Да, продолжить',
                cancelText: 'Нет, отмена',
                confirmClass: 'bg-purple-600 hover:bg-purple-700 text-white',
                onConfirm: () => {
                    showInfoModal({
                        title: 'Успешно!',
                        content: `
                            <div class="text-center py-4">
                                <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 mb-4">
                                    <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <p class="text-gray-600">Действие выполнено успешно!</p>
                            </div>
                        `
                    });
                },
                onCancel: () => {
                    console.log('Действие отменено');
                }
            });
        },

        handleConfirmation() {
            console.log('Подтверждение получено!');
            showInfoModal({
                title: 'Данные удалены',
                content: `
                    <div class="text-center py-4">
                        <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 mb-4">
                            <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </div>
                        <p class="text-gray-600">Все данные успешно удалены.</p>
                    </div>
                `
            });
        },

        saveSettings() {
            console.log('Сохранение настроек...');
            this.$dispatch('close-modal-settingsModal');
            showInfoModal({
                title: 'Настройки сохранены',
                content: `
                    <div class="text-center py-4">
                        <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 mb-4">
                            <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="text-gray-600">Настройки системы успешно сохранены.</p>
                    </div>
                `
            });
        },

        showNotification(message, type = 'info') {
            // Use global notification function if available
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                // Fallback to console
                console.log(`${type.toUpperCase()}: ${message}`);
            }
        }
    }
}
</script>
@endsection 