
@extends('layout')

@section('title', 'Users')

@section('content')
<div x-data="usersComponent()" class="space-y-6">
    <!-- Header with actions -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
        <div class="flex space-x-3">
            <button @click="$dispatch('open-modal-addUserModal')"
                    class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add User
            </button>
        </div>
    </div>

    <!-- Loading state -->
    <div x-show="loading" class="flex justify-center py-8">
        <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm text-gray-700">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        </div>
    </div>

    <!-- Users table -->
    <div x-show="!loading" class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IMSI</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KPI</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OPC</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Associated Plans</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Latency</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="user in users" :key="user.id">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap" x-text="user.imsi"></td>
                            <td class="px-6 py-4 whitespace-nowrap" x-text="user.name"></td>
                            <td class="px-6 py-4 whitespace-nowrap" x-text="user.ip_address"></td>
                            <td class="px-6 py-4 whitespace-nowrap" x-text="user.ki"></td>
                            <td class="px-6 py-4 whitespace-nowrap" x-text="user.opc"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <template x-for="plan in user.plans" :key="plan">
                                    <span class="bg-gray-400 text-white px-2 py-1 m-2 rounded-full text-xs font-semibold" x-text="plan"></span>
                                </template>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap" x-text="user.latency"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button @click="editUser(user)"
                                            class="text-blue-600 hover:text-blue-900 focus:outline-none focus:underline">
                                        Edit
                                    </button>
                                    <button @click="confirmDeleteUser(user)"
                                            class="text-red-600 hover:text-red-900 focus:outline-none focus:underline">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add User Modal -->
    <x-form-modal 
        id="addUserModal" 
        title="Add User"
        submit-text="Create User"
        size="lg"
        action="/api/users"
        method="POST">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="add_name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input type="text" 
                       id="add_name" 
                       name="name" 
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Enter user name">
            </div>
            
            <div>
                <label for="add_email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input type="email" 
                       id="add_email" 
                       name="email" 
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Enter email">
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="add_role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <select id="add_role" 
                        name="role" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select role</option>
                    <option value="admin">Administrator</option>
                    <option value="user">User</option>
                </select>
            </div>
            
            <div>
                <label for="add_status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="add_status" 
                        name="status" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>
        
        <div>
            <label for="add_password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
            <input type="password" 
                   id="add_password" 
                   name="password" 
                   required
                   minlength="8"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Enter password (minimum 8 characters)">
        </div>
    </x-form-modal>

    <!-- Edit User Modal -->
    <x-form-modal 
        id="editUserModal" 
        title="Edit User"
        submit-text="Save Changes"
        size="lg"
        action="/api/users"
        method="PUT">
        
        <input type="hidden" x-model="editingUser.id" name="id">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input type="text" 
                       id="edit_name" 
                       name="name" 
                       required
                       x-model="editingUser.name"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <div>
                <label for="edit_email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input type="email" 
                       id="edit_email" 
                       name="email" 
                       required
                       x-model="editingUser.email"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="edit_role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <select id="edit_role" 
                        name="role" 
                        required
                        x-model="editingUser.role"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="admin">Administrator</option>
                    <option value="user">User</option>
                </select>
            </div>
            
            <div>
                <label for="edit_status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="edit_status" 
                        name="status" 
                        required
                        x-model="editingUser.status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>
        
        <div>
            <label for="edit_password" class="block text-sm font-medium text-gray-700 mb-1">New Password (leave empty if you don't want to change)</label>
            <input type="password" 
                   id="edit_password" 
                   name="password" 
                   minlength="8"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Enter new password">
        </div>
    </x-form-modal>

    <!-- Delete Confirmation Modal -->
    <x-confirm-modal 
        id="deleteUserModal"
        title="Delete User"
        message="Are you sure you want to delete this user? This action cannot be undone."
        confirm-text="Delete"
        cancel-text="Cancel"
        size="md"
        x-on:modal-confirmed-delete-user-modal.window="deleteUser()" />

    <!-- Info Modal -->
    <x-modal 
        id="userInfoModal" 
        title="User Information"
        size="lg"
        animation="fade">
        
        <div x-show="selectedUser" class="space-y-4">
            <div class="flex items-center space-x-4">
                <div class="h-16 w-16 rounded-full bg-blue-500 flex items-center justify-center">
                    <span class="text-white text-xl font-medium" x-text="selectedUser?.name?.charAt(0).toUpperCase()"></span>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900" x-text="selectedUser?.name"></h3>
                    <p class="text-gray-500" x-text="selectedUser?.email"></p>
                </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Role</label>
                    <p class="mt-1 text-sm text-gray-900" x-text="selectedUser?.role"></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    <p class="mt-1 text-sm text-gray-900" x-text="selectedUser?.status === 'active' ? 'Active' : 'Inactive'"></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created Date</label>
                    <p class="mt-1 text-sm text-gray-900" x-text="formatDate(selectedUser?.created_at)"></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                    <p class="mt-1 text-sm text-gray-900" x-text="formatDate(selectedUser?.updated_at)"></p>
                </div>
            </div>
        </div>

        <x-slot name="footer">
            <div class="flex justify-end">
                <button type="button" 
                        class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2"
                        @click="$dispatch('close-modal-userInfoModal')">
                    Close
                </button>
            </div>
        </x-slot>
    </x-modal>
</div>

<script>
function usersComponent() {
    return {
        users: [],
        loading: true,
        editingUser: {},
        selectedUser: null,
        userToDelete: null,

        init() {
            this.loadUsers();
            
            // Listen for modal events
            this.$el.addEventListener('modal-confirmed-deleteUserModal', () => {
                this.deleteUser();
            });
        },

        async loadUsers() {
            try {
                this.loading = true;
                const response = await api.get('/users');
                this.users = response.data || [];
            } catch (error) {
                console.error('Error loading users:', error);
                this.showNotification('Error loading users', 'error');
            } finally {
                this.loading = false;
            }
            console.log('Users loaded:', this.users);
        },

        editUser(user) {
            this.editingUser = { ...user };
            this.$dispatch('open-modal-editUserModal');
        },

        confirmDeleteUser(user) {
            this.userToDelete = user;
            this.$dispatch('open-modal-deleteUserModal');
        },

        async deleteUser() {
            if (!this.userToDelete) return;
            
            try {
                const response = await fetch(`/api/users/${this.userToDelete.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    }
                });

                if (response.ok) {
                    this.users = this.users.filter(u => u.id !== this.userToDelete.id);
                    this.showNotification('User deleted successfully', 'success');
                } else {
                    throw new Error('Failed to delete user');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                this.showNotification('Error deleting user', 'error');
            } finally {
                this.userToDelete = null;
            }
        },

        showUserInfo(user) {
            this.selectedUser = user;
            this.$dispatch('open-modal-userInfoModal');
        },

        formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        },

        showNotification(message, type = 'info') {
            // Implement notification system
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}
</script>
@endsection 