@props([
    'id' => 'formModal',
    'title' => 'Форма',
    'submitText' => 'Сохранить',
    'cancelText' => 'Отмена',
    'submitClass' => 'bg-blue-600 hover:bg-blue-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'lg',
    'method' => 'POST',
    'action' => '#',
    'formId' => null
])

@php
$formId = $formId ?: $id . 'Form';
@endphp

<x-modal :id="$id" :title="$title" :size="$size" animation="slide">
    <x-slot name="body">
        <form id="{{ $formId }}" @submit.prevent="submitForm($event)" class="space-y-4" data-method="{{ $method }}" data-action="{{ $action }}">
            {{ $slot }}
        </form>
    </x-slot>

    <x-slot name="footer">
        <div class="flex justify-end space-x-3">
            <button type="button" 
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 {{ $cancelClass }}"
                    @click="$dispatch('close-modal-{{ $id }}')">
                {{ $cancelText }}
            </button>
            <button type="submit" 
                    form="{{ $formId }}"
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 {{ $submitClass }}">
                {{ $submitText }}
            </button>
        </div>
    </x-slot>
</x-modal> 