@props([
    'id' => 'confirmModal',
    'title' => 'Подтверждение',
    'message' => 'Вы уверены, что хотите выполнить это действие?',
    'confirmText' => 'Подтвердить',
    'cancelText' => 'Отмена',
    'confirmClass' => 'bg-red-600 hover:bg-red-700 text-white',
    'cancelClass' => 'bg-gray-300 hover:bg-gray-400 text-gray-700',
    'size' => 'md'
])

<x-modal :id="$id" :title="$title" :size="$size" animation="zoom">
    <x-slot name="body">
        <div class="text-center">
            <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <p class="text-gray-700 mb-6">{{ $message }}</p>
        </div>
    </x-slot>

    <x-slot name="footer">
        <div class="flex justify-end space-x-3">
            <button type="button" 
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 {{ $cancelClass }}"
                    @click="$dispatch('close-modal-{{ $id }}')">
                {{ $cancelText }}
            </button>
            <button type="button" 
                    class="px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 {{ $confirmClass }}"
                    @click="$dispatch('modal-confirmed-{{ $id }}'); $dispatch('close-modal-{{ $id }}')">
                {{ $confirmText }}
            </button>
        </div>
    </x-slot>
</x-modal> 