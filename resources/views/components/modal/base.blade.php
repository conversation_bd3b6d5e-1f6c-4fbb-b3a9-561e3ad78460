@props([
    'id' => 'modal',
    'size' => 'md', // sm, md, lg, xl, 2xl, 3xl, 4xl, full
    'title' => '',
    'closable' => true,
    'backdrop' => true,
    'centered' => true,
    'animation' => 'fade', // fade, slide, zoom
    'maxWidth' => null,
    'loading' => false
])

@php
$sizeClasses = [
    'sm' => 'max-w-sm',
    'md' => 'max-w-md', 
    'lg' => 'max-w-lg',
    'xl' => 'max-w-xl',
    '2xl' => 'max-w-2xl',
    '3xl' => 'max-w-3xl',
    '4xl' => 'max-w-4xl',
    'full' => 'max-w-full'
];

$animationClasses = [
    'fade' => 'modal-fade',
    'slide' => 'modal-slide', 
    'zoom' => 'modal-zoom'
];

$modalSize = $maxWidth ? "max-w-[$maxWidth]" : ($sizeClasses[$size] ?? $sizeClasses['md']);
$modalAnimation = $animationClasses[$animation] ?? $animationClasses['fade'];
@endphp

<!-- Modal backdrop -->
<div x-data="{ 
        open: false, 
        loading: @js($loading),
        closeModal() {
            if (!this.loading) {
                this.open = false;
            }
        }
    }" 
     x-show="open" 
     x-on:open-modal-{{ $id }}.window="open = true"
     x-on:close-modal-{{ $id }}.window="closeModal()"
     x-on:keydown.escape.window="closeModal()"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto"
     style="display: none;"
     x-cloak>
     
    <!-- Background overlay -->
    @if($backdrop)
    <div class="fixed inset-0 bg-gray-900 bg-opacity-50 transition-opacity"
         @if($closable) @click="closeModal()" @endif>
    </div>
    @endif

    <!-- Modal container -->
    <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0 {{ $centered ? 'items-center' : 'items-start pt-16' }}">
        
        <!-- Modal content -->
        <div x-show="open"
             x-transition:enter="ease-out duration-300 {{ $modalAnimation }}-enter"
             x-transition:enter-start="{{ $modalAnimation }}-enter-start"
             x-transition:enter-end="{{ $modalAnimation }}-enter-end"
             x-transition:leave="ease-in duration-200 {{ $modalAnimation }}-leave"
             x-transition:leave-start="{{ $modalAnimation }}-leave-start"
             x-transition:leave-end="{{ $modalAnimation }}-leave-end"
             @click.stop
             class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all w-full {{ $modalSize }}"
             :class="{ 'pointer-events-none': loading }">
            
            <!-- Loading overlay -->
            <div x-show="loading" 
                 class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10"
                 x-transition>
                <div class="flex items-center space-x-2">
                    <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-sm text-gray-600">Loading...</span>
                </div>
            </div>
            
            <!-- Modal header -->
            @if($title || $closable || isset($header))
            <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                @isset($header)
                    {{ $header }}
                @else
                    <h3 class="text-lg font-medium text-gray-900">
                        {{ $title }}
                    </h3>
                @endisset
                
                @if($closable)
                <button type="button" 
                        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md p-2 disabled:opacity-50"
                        :disabled="loading"
                        @click="closeModal()">
                    <span class="sr-only">Close</span>
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                @endif
            </div>
            @endif

            <!-- Modal body -->
            <div class="px-6 py-4">
                @isset($body)
                    {{ $body }}
                @else
                    {{ $slot }}
                @endisset
            </div>

            <!-- Modal footer -->
            @isset($footer)
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                {{ $footer }}
            </div>
            @endisset
        </div>
    </div>
</div>
