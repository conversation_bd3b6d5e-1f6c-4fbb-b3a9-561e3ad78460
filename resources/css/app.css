@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom components */
@layer components {
    .btn-primary {
        @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }
    
    .btn-secondary {
        @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }
    
    .btn-danger {
        @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }
    
    .form-input {
        @apply w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
    }
    
    .form-select {
        @apply w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
    }
    
    .form-textarea {
        @apply w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical;
    }
    
    .card {
        @apply bg-white rounded-lg shadow-md overflow-hidden;
    }
    
    .card-header {
        @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
    }
    
    .card-body {
        @apply p-6;
    }
    
    .badge-success {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800;
    }
    
    .badge-danger {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800;
    }
    
    .badge-info {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
    }
    
    .badge-warning {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
    }
    
    .table-row-hover {
        @apply hover:bg-gray-50 transition-colors duration-150;
    }
    
    .sidebar-link {
        @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150;
    }
    
    .sidebar-link-active {
        @apply bg-blue-100 text-blue-700;
    }
    
    .sidebar-link-inactive {
        @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900;
    }
    
    .modal-overlay {
        @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
    }
    
    .modal-container {
        @apply relative top-20 mx-auto p-5 border shadow-lg rounded-md bg-white;
    }
    
    .toast {
        @apply max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5;
    }
    
    .toast-success {
        @apply border-l-4 border-green-400;
    }
    
    .toast-error {
        @apply border-l-4 border-red-400;
    }
    
    .toast-info {
        @apply border-l-4 border-blue-400;
    }
    
    .toast-warning {
        @apply border-l-4 border-yellow-400;
    }
}

/* Custom animations */
@layer utilities {
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    .animate-slide-in {
        animation: slideIn 0.3s ease-out;
    }
    
    .animate-bounce-in {
        animation: bounceIn 0.6s ease-out;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Loading spinner */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Status indicators */
.status-online {
    @apply w-2 h-2 bg-green-400 rounded-full;
}

.status-offline {
    @apply w-2 h-2 bg-red-400 rounded-full;
}

.status-warning {
    @apply w-2 h-2 bg-yellow-400 rounded-full;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Modal animations */
.modal-fade-enter {
    transition: opacity 300ms ease-out, transform 300ms ease-out;
}
.modal-fade-enter-start {
    opacity: 0;
    transform: scale(0.95);
}
.modal-fade-enter-end {
    opacity: 1;
    transform: scale(1);
}
.modal-fade-leave {
    transition: opacity 200ms ease-in, transform 200ms ease-in;
}
.modal-fade-leave-start {
    opacity: 1;
    transform: scale(1);
}
.modal-fade-leave-end {
    opacity: 0;
    transform: scale(0.95);
}

.modal-slide-enter {
    transition: opacity 300ms ease-out, transform 300ms ease-out;
}
.modal-slide-enter-start {
    opacity: 0;
    transform: translateY(-50px);
}
.modal-slide-enter-end {
    opacity: 1;
    transform: translateY(0);
}
.modal-slide-leave {
    transition: opacity 200ms ease-in, transform 200ms ease-in;
}
.modal-slide-leave-start {
    opacity: 1;
    transform: translateY(0);
}
.modal-slide-leave-end {
    opacity: 0;
    transform: translateY(-50px);
}

.modal-zoom-enter {
    transition: opacity 300ms ease-out, transform 300ms ease-out;
}
.modal-zoom-enter-start {
    opacity: 0;
    transform: scale(0.8);
}
.modal-zoom-enter-end {
    opacity: 1;
    transform: scale(1);
}
.modal-zoom-leave {
    transition: opacity 200ms ease-in, transform 200ms ease-in;
}
.modal-zoom-leave-start {
    opacity: 1;
    transform: scale(1);
}
.modal-zoom-leave-end {
    opacity: 0;
    transform: scale(0.8);
}