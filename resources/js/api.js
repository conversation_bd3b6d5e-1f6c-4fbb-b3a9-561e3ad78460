/**
 * API Wrapper for RapidSGS Pro
 * Modern fetch wrapper for API communication
 */

class ApiClient {
    constructor(baseURL = '/api') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
    }

    /**
     * Base method for executing HTTP requests
     */
    async request(url, options = {}) {
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        // Add baseURL if URL is relative
        const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

        try {
            const response = await fetch(fullUrl, config);
            
            // Check response status
            if (!response.ok) {
                throw new ApiError(
                    `HTTP ${response.status}: ${response.statusText}`,
                    response.status,
                    response
                );
            }

            // Try to get JSON, if not possible - return text
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            throw new ApiError(`Network error: ${error.message}`, 0, null, error);
        }
    }

    /**
     * GET request
     */
    async get(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const queryString = urlParams.toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return this.request(fullUrl, {
            method: 'GET'
        });
    }

    /**
     * POST request
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT request
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * PATCH request
     */
    async patch(url, data = {}) {
        return this.request(url, {
            method: 'PATCH',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE request
     */
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }

    /**
     * Send FormData (for files)
     */
    async postFormData(url, formData) {
        const headers = { ...this.defaultHeaders };
        delete headers['Content-Type']; // Browser will set correct Content-Type for FormData

        return this.request(url, {
            method: 'POST',
            headers,
            body: formData
        });
    }

    /**
     * Submit form data as FormData
     */
    async submitForm(form, method = 'POST') {
        const formData = new FormData(form);
        const url = form.getAttribute('data-action') || form.action;
        
        // Convert FormData to regular object for JSON
        const data = {};
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                // If key already exists, make it an array
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }

        switch (method.toUpperCase()) {
            case 'GET':
                return this.get(url, data);
            case 'POST':
                return this.post(url, data);
            case 'PUT':
                return this.put(url, data);
            case 'PATCH':
                return this.patch(url, data);
            case 'DELETE':
                return this.delete(url);
            default:
                throw new ApiError(`Unsupported HTTP method: ${method}`);
        }
    }

    /**
     * Set default headers
     */
    setDefaultHeaders(headers) {
        this.defaultHeaders = { ...this.defaultHeaders, ...headers };
    }

    /**
     * Set base URL
     */
    setBaseURL(baseURL) {
        this.baseURL = baseURL;
    }
}

/**
 * API error class
 */
class ApiError extends Error {
    constructor(message, status = 0, response = null, originalError = null) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.response = response;
        this.originalError = originalError;
    }

    /**
     * Get error details for user display
     */
    async getDetails() {
        if (this.response) {
            try {
                const contentType = this.response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await this.response.json();
                } else {
                    return await this.response.text();
                }
            } catch (e) {
                return this.message;
            }
        }
        return this.message;
    }
}

/**
 * Global API client instance
 */
const api = new ApiClient();

/**
 * Function for handling form submission
 */
window.submitForm = async function(event) {
    const form = event.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton?.textContent;
    
    try {
        // Show loading state
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Submitting...';
        }

        const method = form.getAttribute('data-method') || 'POST';
        const response = await api.submitForm(form, method);
        
        // Handle successful response
        console.log('Form submitted successfully:', response);
        
        // Success notification
        showNotification('Data saved successfully', 'success');
        
        // Close modal if exists
        const modalId = form.closest('[x-data]')?.querySelector('[x-on\\:open-modal]')?.getAttribute('x-on:open-modal')?.replace('.window', '').replace('open-modal-', '');
        if (modalId) {
            window.dispatchEvent(new CustomEvent(`close-modal-${modalId}`));
        }
        
        // Clear form
        form.reset();
        
        // Custom event for data refresh
        window.dispatchEvent(new CustomEvent('form-submitted', { 
            detail: { form, response, method } 
        }));

    } catch (error) {
        console.error('Form submission error:', error);
        
        let errorMessage = 'An error occurred while submitting the form';
        
        if (error instanceof ApiError) {
            try {
                const details = await error.getDetails();
                if (typeof details === 'object' && details.message) {
                    errorMessage = details.message;
                } else if (typeof details === 'string') {
                    errorMessage = details;
                }
            } catch (e) {
                errorMessage = error.message;
            }
        } else {
            errorMessage = error.message;
        }
        
        showNotification(errorMessage, 'error');
    } finally {
        // Restore button
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    }
};

/**
 * Function for showing notifications
 */
function showNotification(message, type = 'info') {
    // Simple notification implementation
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // Can be replaced with more advanced notification system
    if (type === 'error') {
        alert(`Error: ${message}`);
    } else if (type === 'success') {
        // Can use toast notifications
        console.log(`✅ ${message}`);
    }
}

/**
 * Export for use in other modules
 */
window.api = api;
window.ApiError = ApiError;

export { api, ApiError }; 