import './bootstrap';
import Alpine from 'alpinejs'
import './api'
import './components/dashboard'
import './components/users'
import './components/plans'
import './components/enbs'
import './components/notifications'
import './components/modal-helpers'

// Make Alpine available globally
window.Alpine = Alpine

// Global Alpine stores
Alpine.store('auth', {
    user: null,
    token: localStorage.getItem('token') || null,
    
    async login(email, password) {
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, password }),
            });
            
            const data = await response.json();
            
            if (response.ok) {
                this.token = data.token;
                this.user = data.user;
                localStorage.setItem('token', data.token);
                return { success: true };
            } else {
                return { success: false, message: data.message };
            }
        } catch (error) {
            return { success: false, message: 'Network error' };
        }
    },
    
    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('token');
        window.location.href = '/login';
    },
    
    isAuthenticated() {
        return !!this.token;
    }
});

Alpine.store('notifications', {
    items: [],
    
    add(type, message, duration = 5000) {
        const id = Date.now();
        this.items.push({ id, type, message });
        
        if (duration > 0) {
            setTimeout(() => this.remove(id), duration);
        }
    },
    
    remove(id) {
        this.items = this.items.filter(item => item.id !== id);
    },
    
    clear() {
        this.items = [];
    }
});

// Global utilities
window.utils = {
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },
    
    formatDateTime(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount || 0);
    },
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    showToast(type, message) {
        Alpine.store('notifications').add(type, message);
    }
};

// CSRF Token setup
const token = document.querySelector('meta[name="csrf-token"]');
if (token) {
    window.csrfToken = token.getAttribute('content');
    
    // Set default headers for fetch requests
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        options.headers = {
            'X-CSRF-TOKEN': window.csrfToken,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...options.headers
        };
        
        // Add auth token if available
        const authToken = Alpine.store('auth').token;
        if (authToken) {
            options.headers['Authorization'] = `Bearer ${authToken}`;
        }
        
        return originalFetch(url, options);
    };
}

// Global utilities
window.showNotification = function(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);
    
    if (type === 'error') {
        alert(`Error: ${message}`);
    } else if (type === 'success') {
        console.log(`✅ ${message}`);
    }
};

// Start Alpine
Alpine.start()

console.log('RapidSGS Pro application loaded successfully'); 