import { api } from '../api'
import Alpine from 'alpinejs'

Alpine.data('dashboardStats', () => ({
    loading: false,
    stats: {
        users: { total: 0, active: 0, inactive: 0 },
        plans: { total: 0, active: 0, inactive: 0 },
        enbs: { total: 0, online: 0, offline: 0 }
    },

    async loadStats() {
        this.loading = true;
        try {
            const response = await api.get('/dashboard/stats');
            this.stats = response.data;
        } catch (error) {
            console.error('Failed to load dashboard stats:', error);
            window.utils.showToast('error', 'Failed to load dashboard statistics');
        } finally {
            this.loading = false;
        }
    }
})); 