import { api } from '../api'
import Alpine from 'alpinejs'

Alpine.data('plansManager', () => ({
    loading: false,
    saving: false,
    plans: [],
    showModal: false,
    modalMode: 'create', // 'create' or 'edit'
    currentPlan: {},

    async loadPlans() {
        this.loading = true;
        try {
            const response = await api.get('/plans');
            this.plans = response.data || response;
        } catch (error) {
            console.error('Failed to load plans:', error);
            window.utils.showToast('error', 'Failed to load plans');
        } finally {
            this.loading = false;
        }
    },

    openCreateModal() {
        this.modalMode = 'create';
        this.currentPlan = { 
            status: 'active',
            support_level: 'basic',
            price: 0
        };
        this.showModal = true;
    },

    editPlan(plan) {
        this.modalMode = 'edit';
        this.currentPlan = { ...plan };
        this.showModal = true;
    },

    closeModal() {
        this.showModal = false;
        this.currentPlan = {};
    },

    async savePlan() {
        this.saving = true;
        try {
            let response;
            if (this.modalMode === 'create') {
                response = await api.post('/plans', this.currentPlan);
            } else {
                response = await api.put(`/plans/${this.currentPlan._id}`, this.currentPlan);
            }

            if (response.success) {
                window.utils.showToast('success', 
                    this.modalMode === 'create' ? 'Plan created successfully' : 'Plan updated successfully'
                );
                this.closeModal();
                await this.loadPlans();
            } else {
                window.utils.showToast('error', response.message || 'Operation failed');
            }
        } catch (error) {
            console.error('Failed to save plan:', error);
            window.utils.showToast('error', 'Failed to save plan');
        } finally {
            this.saving = false;
        }
    },

    async deletePlan(planId) {
        if (confirm('Are you sure you want to delete this plan?')) {
            try {
                const response = await api.delete(`/plans/${planId}`);
                if (response.success) {
                    window.utils.showToast('success', 'Plan deleted successfully');
                    await this.loadPlans();
                } else {
                    window.utils.showToast('error', response.message || 'Delete failed');
                }
            } catch (error) {
                console.error('Failed to delete plan:', error);
                window.utils.showToast('error', 'Failed to delete plan');
            }
        }
    }
})); 