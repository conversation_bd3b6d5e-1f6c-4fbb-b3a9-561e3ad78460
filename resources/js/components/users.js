import { api } from '../api'
import Alpine from 'alpinejs'

Alpine.data('usersManager', () => ({
    loading: false,
    saving: false,
    users: [],
    plans: [],
    filteredUsers: [],
    showModal: false,
    modalMode: 'create', // 'create' or 'edit'
    currentUser: {},
    filters: {
        search: '',
        status: '',
        plan: ''
    },

    async loadUsers() {
        // this.loading = true;
        // try {
        //     const {data: data} = await api.get('/users');
        //     this.users = users;
        // } catch (error) {
        //     console.error('Error loading users:', error);
        //     showNotification('Error loading users', 'error');
        // } finally {
        //     this.loading = false;
        // }
    },

    applyFilters() {
        let filtered = [...this.users];

        // Search filter
        if (this.filters.search) {
            const search = this.filters.search.toLowerCase();
            filtered = filtered.filter(user => 
                (user.name || '').toLowerCase().includes(search) ||
                (user.email || '').toLowerCase().includes(search)
            );
        }

        // Status filter
        if (this.filters.status) {
            filtered = filtered.filter(user => user.status === this.filters.status);
        }

        // Plan filter
        if (this.filters.plan) {
            filtered = filtered.filter(user => user.plan_id === this.filters.plan);
        }

        this.filteredUsers = filtered;
    },

    resetFilters() {
        this.filters = { search: '', status: '', plan: '' };
        this.applyFilters();
    },

    openCreateModal() {
        this.modalMode = 'create';
        this.currentUser = { status: 'active' };
        this.showModal = true;
    },

    editUser(user) {
        this.modalMode = 'edit';
        this.currentUser = { ...user };
        this.showModal = true;
    },

    closeModal() {
        this.showModal = false;
        this.currentUser = {};
    },

    async saveUser() {
        this.saving = true;
        try {
            let response;
            if (this.modalMode === 'create') {
                response = await api.post('/users', this.currentUser);
            } else {
                response = await api.put(`/users/${this.currentUser._id}`, this.currentUser);
            }

            if (response.success) {
                window.utils.showToast('success', 
                    this.modalMode === 'create' ? 'User created successfully' : 'User updated successfully'
                );
                this.closeModal();
                await this.loadUsers();
            } else {
                window.utils.showToast('error', response.message || 'Operation failed');
            }
        } catch (error) {
            console.error('Failed to save user:', error);
            window.utils.showToast('error', 'Failed to save user');
        } finally {
            this.saving = false;
        }
    },

    async deleteUser(userId) {
        if (confirm('Are you sure you want to delete this user?')) {
            try {
                const response = await api.delete(`/users/${userId}`);
                if (response.success) {
                    window.utils.showToast('success', 'User deleted successfully');
                    await this.loadUsers();
                } else {
                    window.utils.showToast('error', response.message || 'Delete failed');
                }
            } catch (error) {
                console.error('Failed to delete user:', error);
                window.utils.showToast('error', 'Failed to delete user');
            }
        }
    },

    formatDate(dateString) {
        return window.utils.formatDate(dateString);
    }
})); 