import { api } from '../api'
import Alpine from 'alpinejs'

Alpine.data('enbsManager', () => ({
    loading: false,
    saving: false,
    enbs: [],
    filteredEnbs: [],
    showModal: false,
    modalMode: 'create', // 'create' or 'edit'
    currentEnb: {},
    filters: {
        search: '',
        status: ''
    },

    async loadEnbs() {
        this.loading = true;
        try {
            const response = await api.get('/enbs');
            this.enbs = response.data || response;
            this.applyFilters();
        } catch (error) {
            console.error('Failed to load eNBs:', error);
            window.utils.showToast('error', 'Failed to load eNBs');
        } finally {
            this.loading = false;
        }
    },

    applyFilters() {
        let filtered = [...this.enbs];

        // Search filter
        if (this.filters.search) {
            const search = this.filters.search.toLowerCase();
            filtered = filtered.filter(enb => 
                (enb.name || '').toLowerCase().includes(search) ||
                (enb.cell_id || '').toString().includes(search) ||
                (enb.location || '').toLowerCase().includes(search)
            );
        }

        // Status filter
        if (this.filters.status) {
            filtered = filtered.filter(enb => enb.status === this.filters.status);
        }

        this.filteredEnbs = filtered;
    },

    resetFilters() {
        this.filters = { search: '', status: '' };
        this.applyFilters();
    },

    openCreateModal() {
        this.modalMode = 'create';
        this.currentEnb = { status: 'active' };
        this.showModal = true;
    },

    editEnb(enb) {
        this.modalMode = 'edit';
        this.currentEnb = { ...enb };
        this.showModal = true;
    },

    closeModal() {
        this.showModal = false;
        this.currentEnb = {};
    },

    async saveEnb() {
        this.saving = true;
        try {
            let response;
            if (this.modalMode === 'create') {
                response = await api.post('/enbs', this.currentEnb);
            } else {
                response = await api.put(`/enbs/${this.currentEnb._id}`, this.currentEnb);
            }

            if (response.success) {
                window.utils.showToast('success', 
                    this.modalMode === 'create' ? 'eNB created successfully' : 'eNB updated successfully'
                );
                this.closeModal();
                await this.loadEnbs();
            } else {
                window.utils.showToast('error', response.message || 'Operation failed');
            }
        } catch (error) {
            console.error('Failed to save eNB:', error);
            window.utils.showToast('error', 'Failed to save eNB');
        } finally {
            this.saving = false;
        }
    },

    async deleteEnb(enbId) {
        if (confirm('Are you sure you want to delete this eNB?')) {
            try {
                const response = await api.delete(`/enbs/${enbId}`);
                if (response.success) {
                    window.utils.showToast('success', 'eNB deleted successfully');
                    await this.loadEnbs();
                } else {
                    window.utils.showToast('error', response.message || 'Delete failed');
                }
            } catch (error) {
                console.error('Failed to delete eNB:', error);
                window.utils.showToast('error', 'Failed to delete eNB');
            }
        }
    },

    formatDate(dateString) {
        return window.utils.formatDate(dateString);
    }
})); 