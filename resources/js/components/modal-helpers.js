/**
 * Modal Helper Functions
 * Utility functions for working with modal windows
 */

/**
 * Open modal window
 */
function openModal(modalId) {
    window.dispatchEvent(new CustomEvent(`open-modal-${modalId}`));
}

/**
 * Close modal window
 */
function closeModal(modalId) {
    window.dispatchEvent(new CustomEvent(`close-modal-${modalId}`));
}

/**
 * Toggle modal window state
 */
function toggleModal(modalId) {
    window.dispatchEvent(new CustomEvent(`toggle-modal-${modalId}`));
}

/**
 * Create and open confirmation modal window
 */
function showConfirmModal({
    title = 'Confirm Action',
    message = 'Are you sure?',
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    confirmClass = 'bg-red-600 hover:bg-red-700 text-white',
    onConfirm = null,
    onCancel = null
}) {
    const modalId = 'dynamicConfirmModal';
    
    // Remove existing modal if present
    const existingContainer = document.getElementById(`${modalId}Container`);
    if (existingContainer) {
        existingContainer.remove();
    }
    
    // Create modal HTML
    const modalHTML = `
        <div id="${modalId}Container" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                
                <!-- This element is to trick the browser into centering the modal contents. -->
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="${modalId}Title">
                                    ${title}
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500" id="${modalId}Message">
                                        ${message}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button" 
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm ${confirmClass}" 
                                id="${modalId}Confirm">
                            ${confirmText}
                        </button>
                        <button type="button" 
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" 
                                id="${modalId}Cancel">
                            ${cancelText}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    const modal = document.getElementById(`${modalId}Container`);
    const confirmBtn = document.getElementById(`${modalId}Confirm`);
    const cancelBtn = document.getElementById(`${modalId}Cancel`);
    const overlay = modal.querySelector('.fixed.inset-0.bg-gray-500');
    
    // Show modal
    modal.style.display = 'block';
    setTimeout(() => {
        modal.classList.add('opacity-100');
        modal.querySelector('.inline-block').classList.add('scale-100');
    }, 10);
    
    // Event handlers
    const handleConfirm = () => {
        if (onConfirm && typeof onConfirm === 'function') {
            onConfirm();
        }
        hideModal();
    };
    
    const handleCancel = () => {
        if (onCancel && typeof onCancel === 'function') {
            onCancel();
        }
        hideModal();
    };
    
    const hideModal = () => {
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
        }, 300);
    };
    
    // Bind events
    confirmBtn.addEventListener('click', handleConfirm);
    cancelBtn.addEventListener('click', handleCancel);
    overlay.addEventListener('click', handleCancel);
    
    // ESC key handler
    const handleEsc = (e) => {
        if (e.key === 'Escape') {
            handleCancel();
            document.removeEventListener('keydown', handleEsc);
        }
    };
    document.addEventListener('keydown', handleEsc);
    
    return {
        confirm: handleConfirm,
        cancel: handleCancel,
        hide: hideModal
    };
}

/**
 * Create and show info modal
 */
function showInfoModal({
    title = 'Information',
    message = '',
    buttonText = 'Close',
    onClose = null
}) {
    const modalId = 'dynamicInfoModal';
    
    // Remove existing modal if present
    const existingContainer = document.getElementById(`${modalId}Container`);
    if (existingContainer) {
        existingContainer.remove();
    }
    
    // Create modal HTML
    const modalHTML = `
        <div id="${modalId}Container" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                
                <!-- This element is to trick the browser into centering the modal contents. -->
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    ${title}
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">
                                        ${message}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button" 
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm" 
                                id="${modalId}Close">
                            ${buttonText}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    const modal = document.getElementById(`${modalId}Container`);
    const closeBtn = document.getElementById(`${modalId}Close`);
    const overlay = modal.querySelector('.fixed.inset-0.bg-gray-500');
    
    // Show modal
    modal.style.display = 'block';
    setTimeout(() => {
        modal.classList.add('opacity-100');
        modal.querySelector('.inline-block').classList.add('scale-100');
    }, 10);
    
    // Event handlers
    const handleClose = () => {
        if (onClose && typeof onClose === 'function') {
            onClose();
        }
        hideModal();
    };
    
    const hideModal = () => {
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
        }, 300);
    };
    
    // Bind events
    closeBtn.addEventListener('click', handleClose);
    overlay.addEventListener('click', handleClose);
    
    // ESC key handler
    const handleEsc = (e) => {
        if (e.key === 'Escape') {
            handleClose();
            document.removeEventListener('keydown', handleEsc);
        }
    };
    document.addEventListener('keydown', handleEsc);
    
    return {
        close: handleClose,
        hide: hideModal
    };
}

/**
 * Close all modal windows
 */
function closeAllModals() {
    // Close standard modals
    const modals = document.querySelectorAll('[x-data*="modal"]');
    modals.forEach(modal => {
        if (modal.style.display !== 'none') {
            const modalId = modal.id;
            if (modalId) {
                closeModal(modalId);
            }
        }
    });
    
    // Close dynamic modals
    const dynamicModals = document.querySelectorAll('[id$="Container"]');
    dynamicModals.forEach(modal => {
        if (modal.id.includes('Modal')) {
            modal.remove();
        }
    });
}

// Export functions to global scope
window.openModal = openModal;
window.closeModal = closeModal;
window.toggleModal = toggleModal;
window.showConfirmModal = showConfirmModal;
window.showInfoModal = showInfoModal;
window.closeAllModals = closeAllModals; 