# RapidSGS Pro - Production Deployment Guide

Это руководство описывает деплой приложения в продакшен **без использования Docker**.

## Системные требования

- **PHP 8.2+** с расширениями:
  - mongodb
  - mbstring
  - xml
  - curl
  - gd
  - zip
  - bcmath
- **Node.js 18+** и npm
- **MongoDB 7.0+**
- **Nginx** или **Apache**
- **Composer**

## Подготовка сервера

### 1. Установка PHP 8.2 и расширений (Ubuntu/Debian)

```bash
# Добавить репозиторий PHP
sudo apt update
sudo apt install software-properties-common
sudo add-apt-repository ppa:ondrej/php
sudo apt update

# Установить PHP и расширения
sudo apt install php8.2-fpm php8.2-cli php8.2-common \
    php8.2-mongodb php8.2-mbstring php8.2-xml \
    php8.2-curl php8.2-gd php8.2-zip php8.2-bcmath

# Проверить установку
php -v
php -m | grep mongodb
```

### 2. Установка MongoDB

```bash
# Установить MongoDB
curl -fsSL https://pgp.mongodb.com/server-7.0.asc | sudo gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list
sudo apt update
sudo apt install -y mongodb-org

# Запустить MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

### 3. Установка Node.js

```bash
# Установить Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## Деплой приложения

### 1. Загрузка кода

```bash
# Клонировать проект
cd /var/www/html
sudo git clone https://github.com/your-repo/rapid5gs.com.git
cd rapid5gs.com/www

# Установить права доступа
sudo chown -R www-data:www-data /var/www/html/rapid5gs.com
sudo chmod -R 755 /var/www/html/rapid5gs.com
```

### 2. Настройка окружения

```bash
# Скопировать файл окружения
cp .env.example .env

# Настроить .env файл
nano .env
```

Важные параметры в `.env`:

```env
APP_NAME="RapidSGS Pro"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://rapid5gs.com

DB_CONNECTION=mongodb
DB_HOST=127.0.0.1
DB_PORT=27017
DB_DATABASE=rapid5gs
DB_USERNAME=
DB_PASSWORD=

VIEW_COMPILED_PATH=/var/www/html/rapid5gs.com/www/storage/framework/views
```

### 3. Автоматический деплой

```bash
# Запустить скрипт деплоя
./deploy.sh production
```

Или вручную:

```bash
# Установить зависимости
composer install --no-dev --optimize-autoloader

# Собрать frontend ресурсы
npm ci
npm run build

# Настроить права доступа
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# Очистить кеш
rm -rf storage/framework/views/*
```

## Настройка Nginx

### 1. Создать конфигурацию сайта

```bash
sudo nano /etc/nginx/sites-available/rapid5gs.com
```

Использовать содержимое из `nginx.conf`:

```nginx
server {
    listen 80;
    server_name rapid5gs.com www.rapid5gs.com;
    root /var/www/html/rapid5gs.com/www/public;
    index index.php;

    # Включить gzip сжатие
    gzip on;
    gzip_vary on;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Кеширование статических ресурсов
    location /build/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        try_files $uri =404;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }

    # PHP маршруты
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    # Блокировка доступа к служебным файлам
    location ~ /\. {
        deny all;
    }
    
    location ~* /(storage|vendor|bootstrap|config)/.*$ {
        deny all;
    }
}
```

### 2. Активировать сайт

```bash
# Активировать конфигурацию
sudo ln -s /etc/nginx/sites-available/rapid5gs.com /etc/nginx/sites-enabled/

# Проверить конфигурацию
sudo nginx -t

# Перезапустить Nginx
sudo systemctl reload nginx
```

## SSL сертификат (Let's Encrypt)

```bash
# Установить Certbot
sudo apt install certbot python3-certbot-nginx

# Получить сертификат
sudo certbot --nginx -d rapid5gs.com -d www.rapid5gs.com

# Автообновление
sudo systemctl enable certbot.timer
```

## Мониторинг и обслуживание

### Логи приложения

```bash
# Логи PHP-FPM
sudo tail -f /var/log/php8.2-fpm.log

# Логи Nginx
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# Логи приложения
tail -f /var/www/html/rapid5gs.com/www/storage/logs/lumen.log
```

### Обновление приложения

```bash
cd /var/www/html/rapid5gs.com/www

# Получить обновления
git pull origin main

# Обновить зависимости и пересобрать
./deploy.sh production

# Перезапустить PHP-FPM
sudo systemctl reload php8.2-fpm
```

### Backup базы данных

```bash
# Создать backup MongoDB
mongodump --db rapid5gs --out /backup/$(date +%Y%m%d)

# Автоматический backup (добавить в crontab)
0 2 * * * mongodump --db rapid5gs --out /backup/$(date +\%Y\%m\%d)
```

## Производительность

### Настройка PHP-FPM

```bash
sudo nano /etc/php/8.2/fpm/pool.d/www.conf
```

Рекомендуемые параметры:

```ini
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

### Оптимизация MongoDB

```bash
# Создать индексы для лучшей производительности
mongo rapid5gs --eval "
db.users.createIndex({email: 1});
db.plans.createIndex({status: 1});
db.enbs.createIndex({status: 1, location: 1});
"
```

## Безопасность

1. **Firewall**: Настройте UFW для ограничения доступа
2. **PHP**: Отключите ненужные функции в php.ini
3. **MongoDB**: Настройте аутентификацию
4. **Nginx**: Скройте версию сервера
5. **SSL**: Используйте только HTTPS в продакшене

## Troubleshooting

### Проблемы с правами доступа

```bash
sudo chown -R www-data:www-data storage
sudo chmod -R 755 storage
```

### Проблемы с MongoDB подключением

```bash
# Проверить статус MongoDB
sudo systemctl status mongod

# Проверить подключение
mongo --eval "db.adminCommand('ismaster')"
```

### Очистка кешей

```bash
rm -rf storage/framework/views/*
sudo systemctl reload php8.2-fpm
``` 